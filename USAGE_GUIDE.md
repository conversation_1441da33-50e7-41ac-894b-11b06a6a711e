# 🤖 Simple CLI Chatbot - Usage Guide

## Quick Start

### 1. Run the Chatbot
```bash
# Windows
start_chatbot.bat

# Linux/Mac
source chatbot_env/bin/activate
python cli_chatbot.py
```

### 2. Basic Usage
```
You: Hello, how are you?
Bot: I'm doing well, thank you! How can I help you today?

You: /help
Available Commands:
  /help     - Show this help message
  /info     - Show model information
  /clear    - Clear conversation history
  /history  - Show conversation history
  /model    - Change model (e.g., /model meta-llama/Llama-3.2-3B)
  /quit     - Exit the chatbot
```

## Changing Models

### Method 1: Using Chat Commands
```
You: /model microsoft/DialoGPT-medium
🔄 Changing model to: microsoft/DialoGPT-medium
✅ Model changed successfully!
```

### Method 2: Edit Configuration File
Edit `chatbot_config.yaml`:
```yaml
model:
  name: "meta-llama/Llama-3.2-3B"  # Change this line
```

### Popular Models to Try
- `meta-llama/Llama-3.2-1B` (Default - 1.2GB)
- `meta-llama/Llama-3.2-3B` (Better quality - 3.2GB)
- `microsoft/DialoGPT-medium` (Conversation focused - 350MB)
- `microsoft/DialoGPT-large` (Better conversations - 775MB)
- `facebook/blenderbot-400M-distill` (Fast responses - 400MB)

## Configuration Options

### Model Parameters (`chatbot_config.yaml`)
```yaml
model:
  name: "meta-llama/Llama-3.2-1B"
  temperature: 0.7        # Creativity (0.1-1.0)
  max_new_tokens: 256     # Response length
  top_p: 0.9             # Nucleus sampling
  top_k: 50              # Top-k sampling
  repetition_penalty: 1.1 # Avoid repetition
```

### Device Settings
```yaml
device:
  type: "auto"           # auto, cuda, or cpu
  cuda_version: "11.8"
```

### Chat Settings
```yaml
chat:
  system_prompt: "You are a helpful AI assistant."
  max_history: 10        # Messages to remember
```

## System Requirements

### Minimum Requirements
- **RAM**: 4GB
- **Storage**: 3GB free space
- **Python**: 3.8+

### Recommended for GPU
- **VRAM**: 4GB+ (RTX 3050 Ti or better)
- **CUDA**: 11.8 or compatible
- **RAM**: 8GB+

## Performance Tips

### For Low Memory Systems
1. Use smaller models:
   - `microsoft/DialoGPT-medium` (350MB)
   - `facebook/blenderbot-400M-distill` (400MB)

2. Reduce parameters:
   ```yaml
   model:
     max_new_tokens: 128  # Shorter responses
   chat:
     max_history: 5       # Less context
   ```

### For Better Quality
1. Use larger models:
   - `meta-llama/Llama-3.2-3B`
   - `microsoft/DialoGPT-large`

2. Adjust parameters:
   ```yaml
   model:
     temperature: 0.8     # More creative
     max_new_tokens: 512  # Longer responses
   ```

## Troubleshooting

### Model Loading Issues
```bash
# Check GPU status
You: /info

# Try CPU mode
# Edit chatbot_config.yaml:
device:
  type: "cpu"
```

### Memory Issues
1. **Out of Memory Error**:
   - Use smaller model
   - Restart chatbot
   - Close other applications

2. **Slow Performance**:
   - Check if using GPU: `/info`
   - Reduce `max_new_tokens`
   - Use CPU if GPU is overloaded

### Connection Issues
1. **Model Download Fails**:
   - Check internet connection
   - Try again (models are cached)
   - Use different model

## Advanced Usage

### Custom System Prompts
Edit `chatbot_config.yaml`:
```yaml
chat:
  system_prompt: "You are a Python programming expert. Help with coding questions."
```

### Logging
- Logs saved to `chatbot.log`
- Change log level in config:
  ```yaml
  logging:
    level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
  ```

### Testing
```bash
# Test installation
source chatbot_env/Scripts/activate
python test_chatbot.py
```

## File Structure
```
simple_chatbot/
├── chatbot_config.yaml      # Main configuration
├── cli_chatbot.py          # Main chatbot script
├── config_manager.py       # Configuration handler
├── model_loader.py         # Model loading logic
├── start_chatbot.bat       # Windows launcher
├── test_chatbot.py         # Test script
├── chatbot_env/            # Python virtual environment
├── models/                 # Downloaded model cache
└── README_CHATBOT.md       # Full documentation
```

## Getting Help

1. **In-chat help**: Type `/help`
2. **Model info**: Type `/info`
3. **Check logs**: View `chatbot.log`
4. **Test system**: Run `python test_chatbot.py`

## Tips for Best Experience

1. **Start conversations clearly**: "Hello" or "Hi there"
2. **Be specific**: "Explain Python functions" vs "Help with code"
3. **Use commands**: `/clear` to start fresh conversations
4. **Check memory**: Use `/info` to monitor GPU usage
5. **Experiment**: Try different models for different tasks
