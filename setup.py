#!/usr/bin/env python3
"""
Setup script for Simple CLI Chatbot
Automated installation and configuration
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path


class ChatbotSetup:
    """Handles automated setup of the chatbot"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_cmd = self._get_python_command()
        self.venv_name = "chatbot_env"
        self.requirements = [
            "torch>=2.0.0",
            "transformers>=4.30.0",
            "accelerate>=0.20.0",
            "pyyaml>=6.0",
            "colorama>=0.4.6",
            "tqdm>=4.65.0",
            "huggingface-hub>=0.15.0",
            "bitsandbytes>=0.41.0",
            "psutil>=5.9.0"
        ]
    
    def _get_python_command(self):
        """Get the appropriate Python command"""
        for cmd in ["python3", "python"]:
            try:
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True)
                if result.returncode == 0 and "3." in result.stdout:
                    return cmd
            except FileNotFoundError:
                continue
        
        print("❌ Python 3 not found. Please install Python 3.8 or later.")
        sys.exit(1)
    
    def check_requirements(self):
        """Check system requirements"""
        print("🔍 Checking system requirements...")
        
        # Check Python version
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8 or later is required")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        
        # Check available disk space (at least 5GB)
        disk_usage = shutil.disk_usage(".")
        free_gb = disk_usage.free / (1024**3)
        if free_gb < 5:
            print(f"⚠️  Warning: Only {free_gb:.1f}GB free space available")
            print("   Recommend at least 5GB for models and cache")
        else:
            print(f"✅ Disk space: {free_gb:.1f}GB available")
        
        # Check if CUDA is available
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✅ CUDA available: {torch.version.cuda}")
                print(f"   GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("⚠️  CUDA not available - will use CPU mode")
        except ImportError:
            print("ℹ️  PyTorch not installed yet - will check CUDA after installation")
        
        return True
    
    def create_virtual_environment(self):
        """Create Python virtual environment"""
        print(f"\n🔧 Creating virtual environment: {self.venv_name}")
        
        if Path(self.venv_name).exists():
            print(f"⚠️  Virtual environment {self.venv_name} already exists")
            response = input("Do you want to recreate it? (y/N): ").lower()
            if response == 'y':
                shutil.rmtree(self.venv_name)
            else:
                print("✅ Using existing virtual environment")
                return True
        
        try:
            subprocess.run([self.python_cmd, "-m", "venv", self.venv_name], 
                         check=True)
            print(f"✅ Virtual environment created: {self.venv_name}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False
    
    def get_activation_command(self):
        """Get the command to activate virtual environment"""
        if self.system == "windows":
            return f"{self.venv_name}\\Scripts\\activate.bat"
        else:
            return f"source {self.venv_name}/bin/activate"
    
    def get_pip_command(self):
        """Get the pip command for the virtual environment"""
        if self.system == "windows":
            return f"{self.venv_name}\\Scripts\\pip.exe"
        else:
            return f"{self.venv_name}/bin/pip"
    
    def install_pytorch(self):
        """Install PyTorch with CUDA support"""
        print("\n🔧 Installing PyTorch with CUDA 11.8 support...")
        
        pip_cmd = self.get_pip_command()
        
        # Install PyTorch with CUDA 11.8
        pytorch_cmd = [
            pip_cmd, "install", "torch", "torchvision", "torchaudio",
            "--index-url", "https://download.pytorch.org/whl/cu118"
        ]
        
        try:
            subprocess.run(pytorch_cmd, check=True)
            print("✅ PyTorch installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install PyTorch: {e}")
            print("🔄 Trying CPU-only version...")
            
            # Fallback to CPU version
            cpu_cmd = [pip_cmd, "install", "torch", "torchvision", "torchaudio"]
            try:
                subprocess.run(cpu_cmd, check=True)
                print("✅ PyTorch (CPU) installed successfully")
                return True
            except subprocess.CalledProcessError as e2:
                print(f"❌ Failed to install PyTorch (CPU): {e2}")
                return False
    
    def install_requirements(self):
        """Install other requirements"""
        print("\n🔧 Installing other requirements...")
        
        pip_cmd = self.get_pip_command()
        
        # Install other packages
        other_requirements = [req for req in self.requirements if not req.startswith("torch")]
        
        for req in other_requirements:
            print(f"   Installing {req}...")
            try:
                subprocess.run([pip_cmd, "install", req], check=True)
            except subprocess.CalledProcessError as e:
                print(f"⚠️  Warning: Failed to install {req}: {e}")
        
        print("✅ Requirements installation completed")
        return True
    
    def create_startup_scripts(self):
        """Create startup scripts for different platforms"""
        print("\n🔧 Creating startup scripts...")
        
        if self.system == "windows":
            # Windows batch file
            batch_content = f"""@echo off
echo Starting Simple CLI Chatbot...
echo.

REM Activate virtual environment
call {self.venv_name}\\Scripts\\activate.bat

REM Run the chatbot
python cli_chatbot.py

REM Deactivate virtual environment
deactivate

echo.
echo Chatbot session ended.
pause
"""
            with open("start_chatbot.bat", "w") as f:
                f.write(batch_content)
            print("✅ Created start_chatbot.bat")
        
        else:
            # Unix shell script
            shell_content = f"""#!/bin/bash
echo "Starting Simple CLI Chatbot..."
echo

# Activate virtual environment
source {self.venv_name}/bin/activate

# Run the chatbot
python cli_chatbot.py

# Deactivate virtual environment
deactivate

echo
echo "Chatbot session ended."
"""
            with open("start_chatbot.sh", "w") as f:
                f.write(shell_content)
            
            # Make executable
            os.chmod("start_chatbot.sh", 0o755)
            print("✅ Created start_chatbot.sh")
    
    def test_installation(self):
        """Test the installation"""
        print("\n🧪 Testing installation...")
        
        pip_cmd = self.get_pip_command()
        
        # Test if we can import required packages
        test_script = """
import sys
try:
    import torch
    import transformers
    import yaml
    import colorama
    print("✅ All packages imported successfully")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.version.cuda}")
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("ℹ️  CUDA not available - using CPU mode")
    
    print(f"✅ PyTorch version: {torch.__version__}")
    print(f"✅ Transformers version: {transformers.__version__}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
        
        python_cmd = self.get_pip_command().replace("pip", "python")
        if self.system == "windows":
            python_cmd = python_cmd.replace(".exe", ".exe")
        
        try:
            result = subprocess.run([python_cmd, "-c", test_script], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(result.stdout)
                return True
            else:
                print(f"❌ Test failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Test error: {e}")
            return False
    
    def run_setup(self):
        """Run the complete setup process"""
        print("🤖 Simple CLI Chatbot Setup")
        print("=" * 40)
        
        if not self.check_requirements():
            return False
        
        if not self.create_virtual_environment():
            return False
        
        if not self.install_pytorch():
            return False
        
        if not self.install_requirements():
            return False
        
        self.create_startup_scripts()
        
        if not self.test_installation():
            print("\n⚠️  Installation completed with warnings")
            print("   You may need to install additional dependencies manually")
        else:
            print("\n🎉 Setup completed successfully!")
        
        print("\n📋 Next steps:")
        if self.system == "windows":
            print("   1. Run: start_chatbot.bat")
        else:
            print("   1. Run: ./start_chatbot.sh")
        print("   2. Or manually activate the environment and run:")
        print(f"      {self.get_activation_command()}")
        print("      python cli_chatbot.py")
        
        return True


def main():
    """Main setup function"""
    setup = ChatbotSetup()
    
    try:
        success = setup.run_setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
