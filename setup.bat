@echo off
REM Setup script for the chatbot environment

echo ================================================
echo   Simple CLI Chatbot - Environment Setup
echo ================================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Create virtual environment
echo.
echo Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo Error: Failed to create virtual environment
    pause
    exit /b 1
)

echo Virtual environment created successfully!

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install PyTorch with CUDA 11.8 support
echo.
echo Installing PyTorch with CUDA 11.8 support...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

REM Install other requirements
echo.
echo Installing other requirements...
pip install transformers>=4.30.0 accelerate>=0.20.0 huggingface-hub>=0.15.0 colorama>=0.4.6 pyyaml>=6.0

echo.
echo ================================================
echo   Setup completed successfully!
echo ================================================
echo.
echo To run the chatbot:
echo   1. Open Command Prompt (cmd)
echo   2. Navigate to this directory
echo   3. Run: run_chatbot.bat
echo.
echo To change the model:
echo   1. Edit config.yaml
echo   2. Change the 'name' field under 'model'
echo   3. Example models:
echo      - openai-community/gpt2-large
echo      - meta-llama/Llama-3.2-1B
echo      - microsoft/DialoGPT-medium
echo.
pause
