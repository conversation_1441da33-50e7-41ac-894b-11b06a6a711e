"""
Conversation Templates for the Chatbot
Pre-defined templates for different use cases and conversation styles.
"""

from typing import Dict, List
import json
from pathlib import Path


class ConversationTemplates:
    """Manages conversation templates for different use cases"""
    
    def __init__(self, templates_dir: str = "templates"):
        """
        Initialize the conversation templates manager
        
        Args:
            templates_dir: Directory to store template files
        """
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        
        # Initialize with built-in templates
        self._create_builtin_templates()
    
    def _create_builtin_templates(self) -> None:
        """Create built-in conversation templates"""
        builtin_templates = {
            "general": {
                "name": "General Assistant",
                "description": "Helpful AI assistant for general questions",
                "system_prompt": "You are a helpful AI assistant. Please provide clear, accurate, and concise responses to help the user with their questions.",
                "starter_messages": [
                    "Hello! How can I help you today?",
                    "Hi there! What would you like to know?",
                    "Welcome! I'm here to assist you with any questions."
                ],
                "sample_conversations": [
                    {
                        "user": "What's the weather like?",
                        "bot": "I don't have access to current weather data, but I can help you find weather information or discuss weather-related topics!"
                    }
                ]
            },
            
            "coding": {
                "name": "Programming Assistant",
                "description": "AI assistant specialized in programming and software development",
                "system_prompt": "You are an expert programming assistant. Help users with coding questions, debugging, code review, and software development best practices. Provide clear explanations and working code examples when possible.",
                "starter_messages": [
                    "Hello! I'm here to help with your programming questions.",
                    "Hi! What coding challenge can I help you solve today?",
                    "Welcome! Ready to dive into some code?"
                ],
                "sample_conversations": [
                    {
                        "user": "How do I create a list in Python?",
                        "bot": "In Python, you can create a list using square brackets:\n\n```python\n# Empty list\nmy_list = []\n\n# List with items\nfruits = ['apple', 'banana', 'orange']\n\n# List with numbers\nnumbers = [1, 2, 3, 4, 5]\n```"
                    }
                ]
            },
            
            "creative": {
                "name": "Creative Writing Assistant",
                "description": "AI assistant for creative writing, storytelling, and brainstorming",
                "system_prompt": "You are a creative writing assistant. Help users with storytelling, character development, plot ideas, creative brainstorming, and writing techniques. Be imaginative and inspiring while providing practical writing advice.",
                "starter_messages": [
                    "Hello, fellow writer! What creative project are you working on?",
                    "Hi! Ready to explore some creative ideas together?",
                    "Welcome to our creative space! What story shall we tell?"
                ],
                "sample_conversations": [
                    {
                        "user": "I need help creating a character for my story.",
                        "bot": "I'd love to help you create a compelling character! Let's start with the basics:\n\n1. What genre is your story?\n2. What role will this character play (protagonist, antagonist, supporting)?\n3. Do you have any initial ideas about their personality or background?\n\nOnce I know more, I can help you develop their backstory, motivations, and unique traits!"
                    }
                ]
            },
            
            "learning": {
                "name": "Learning Tutor",
                "description": "AI tutor for educational topics and learning assistance",
                "system_prompt": "You are a patient and knowledgeable tutor. Help users learn new topics by breaking down complex concepts into simple, understandable parts. Use examples, analogies, and step-by-step explanations. Encourage questions and provide positive reinforcement.",
                "starter_messages": [
                    "Hello, student! What would you like to learn about today?",
                    "Hi! I'm here to help you understand any topic you're curious about.",
                    "Welcome to our learning session! What subject interests you?"
                ],
                "sample_conversations": [
                    {
                        "user": "Can you explain photosynthesis?",
                        "bot": "Absolutely! Photosynthesis is how plants make their own food using sunlight. Think of it like a plant's kitchen:\n\n🌱 **Ingredients needed:**\n- Sunlight (energy source)\n- Water (from roots)\n- Carbon dioxide (from air)\n\n🍃 **The process:**\n1. Leaves capture sunlight\n2. Chlorophyll (green stuff) converts light to energy\n3. Plant combines water + CO2 + energy = glucose (sugar food)\n4. Oxygen is released as a bonus!\n\n**Simple equation:** Water + CO2 + Sunlight → Glucose + Oxygen\n\nThis is why plants are green and why we have oxygen to breathe! Any questions about this process?"
                    }
                ]
            },
            
            "casual": {
                "name": "Casual Chat",
                "description": "Friendly, casual conversation partner",
                "system_prompt": "You are a friendly, casual conversation partner. Chat naturally about everyday topics, share interesting thoughts, and be a good listener. Keep the tone light and engaging, like talking with a good friend.",
                "starter_messages": [
                    "Hey! How's your day going?",
                    "Hi there! What's on your mind?",
                    "Hello! Nice to chat with you!"
                ],
                "sample_conversations": [
                    {
                        "user": "I'm feeling a bit stressed today.",
                        "bot": "Oh no, sorry to hear that! Stress can be really tough. Want to talk about what's bothering you? Sometimes just getting it out there can help. Or if you'd prefer, we could chat about something completely different to take your mind off things for a bit. What sounds better to you?"
                    }
                ]
            },
            
            "business": {
                "name": "Business Advisor",
                "description": "AI assistant for business and professional topics",
                "system_prompt": "You are a knowledgeable business advisor. Help users with business strategy, professional development, workplace challenges, and career advice. Provide practical, actionable insights while maintaining a professional tone.",
                "starter_messages": [
                    "Hello! How can I assist you with your business or career today?",
                    "Hi! What professional challenge can I help you tackle?",
                    "Welcome! Ready to discuss business strategy and growth?"
                ],
                "sample_conversations": [
                    {
                        "user": "How do I improve team productivity?",
                        "bot": "Great question! Here are some proven strategies to boost team productivity:\n\n**1. Clear Goals & Expectations**\n- Set SMART objectives\n- Communicate priorities clearly\n- Regular check-ins\n\n**2. Remove Obstacles**\n- Identify bottlenecks\n- Streamline processes\n- Provide necessary tools/resources\n\n**3. Foster Collaboration**\n- Encourage open communication\n- Use collaboration tools\n- Regular team meetings\n\n**4. Recognize & Reward**\n- Acknowledge good work\n- Celebrate milestones\n- Provide growth opportunities\n\nWhat specific productivity challenges is your team facing? I can provide more targeted advice!"
                    }
                ]
            }
        }
        
        # Save built-in templates
        for template_id, template_data in builtin_templates.items():
            self.save_template(template_id, template_data)
    
    def get_template(self, template_id: str) -> Dict:
        """
        Get a conversation template by ID
        
        Args:
            template_id: ID of the template
            
        Returns:
            Dict: Template data
        """
        template_file = self.templates_dir / f"{template_id}.json"
        
        if not template_file.exists():
            raise FileNotFoundError(f"Template not found: {template_id}")
        
        with open(template_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_template(self, template_id: str, template_data: Dict) -> None:
        """
        Save a conversation template
        
        Args:
            template_id: ID for the template
            template_data: Template data
        """
        template_file = self.templates_dir / f"{template_id}.json"
        
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2, ensure_ascii=False)
    
    def list_templates(self) -> List[Dict[str, str]]:
        """
        List all available templates
        
        Returns:
            List[Dict]: Template summaries
        """
        templates = []
        
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                templates.append({
                    "id": template_file.stem,
                    "name": data.get("name", "Unknown"),
                    "description": data.get("description", "No description")
                })
            except (json.JSONDecodeError, KeyError):
                continue
        
        return sorted(templates, key=lambda x: x["name"])
    
    def apply_template(self, template_id: str) -> Dict:
        """
        Apply a template and get the configuration
        
        Args:
            template_id: ID of the template to apply
            
        Returns:
            Dict: Template configuration for the chatbot
        """
        template = self.get_template(template_id)
        
        return {
            "system_prompt": template.get("system_prompt", ""),
            "starter_message": template.get("starter_messages", ["Hello!"])[0],
            "template_name": template.get("name", "Unknown Template")
        }
    
    def create_custom_template(self, template_id: str, name: str, description: str, 
                             system_prompt: str, starter_messages: List[str] = None) -> None:
        """
        Create a custom conversation template
        
        Args:
            template_id: Unique ID for the template
            name: Display name
            description: Template description
            system_prompt: System prompt for the AI
            starter_messages: Optional starter messages
        """
        if starter_messages is None:
            starter_messages = ["Hello! How can I help you?"]
        
        template_data = {
            "name": name,
            "description": description,
            "system_prompt": system_prompt,
            "starter_messages": starter_messages,
            "custom": True
        }
        
        self.save_template(template_id, template_data)
    
    def delete_template(self, template_id: str) -> bool:
        """
        Delete a template (only custom templates)
        
        Args:
            template_id: ID of the template to delete
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            template = self.get_template(template_id)
            
            # Only allow deletion of custom templates
            if not template.get("custom", False):
                return False
            
            template_file = self.templates_dir / f"{template_id}.json"
            template_file.unlink()
            return True
            
        except (FileNotFoundError, KeyError):
            return False


if __name__ == "__main__":
    # Test the templates
    templates = ConversationTemplates()
    
    print("Available templates:")
    for template in templates.list_templates():
        print(f"  {template['id']}: {template['name']}")
        print(f"    {template['description']}")
    
    print("\nTesting coding template:")
    coding_config = templates.apply_template("coding")
    print(f"System prompt: {coding_config['system_prompt'][:100]}...")
    print(f"Starter: {coding_config['starter_message']}")
