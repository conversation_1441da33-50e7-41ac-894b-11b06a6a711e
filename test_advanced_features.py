#!/usr/bin/env python3
"""
Advanced Features Test for the Chatbot
Tests conversation management, templates, and performance monitoring.
"""

import time
from config_manager import Config<PERSON>anager
from conversation_manager import ConversationManager
from conversation_templates import ConversationTemplates
from model_manager import <PERSON><PERSON>anager
from performance_monitor import PerformanceMonitor


def test_conversation_management():
    """Test conversation management features"""
    print("🧪 Testing Conversation Management...")
    
    manager = ConversationManager()
    
    # Test conversation data
    test_conversation = [
        {"user": "Hello!", "bot": "Hi there! How can I help you?"},
        {"user": "What's Python?", "bot": "Python is a programming language known for its simplicity and readability."},
        {"user": "Thanks!", "bot": "You're welcome! Feel free to ask more questions."}
    ]
    
    # Test saving
    filename = manager.save_conversation(
        test_conversation, 
        "test_advanced_features",
        {"model_name": "test-model", "test": True}
    )
    print(f"   ✅ Saved conversation: {filename}")
    
    # Test loading
    loaded_data = manager.load_conversation("test_advanced_features")
    assert len(loaded_data["conversation"]) == 3
    print("   ✅ Loaded conversation successfully")
    
    # Test listing
    conversations = manager.list_conversations()
    assert len(conversations) > 0
    print(f"   ✅ Listed {len(conversations)} conversations")
    
    # Test searching
    results = manager.search_conversations("Python")
    assert len(results) > 0
    print(f"   ✅ Found {len(results)} conversations with 'Python'")
    
    # Test stats
    stats = manager.get_conversation_stats()
    print(f"   ✅ Stats: {stats['total_conversations']} conversations, {stats['total_size']}")
    
    return True


def test_conversation_templates():
    """Test conversation templates"""
    print("\n🧪 Testing Conversation Templates...")
    
    templates = ConversationTemplates()
    
    # Test listing templates
    template_list = templates.list_templates()
    assert len(template_list) > 0
    print(f"   ✅ Found {len(template_list)} templates")
    
    # Test applying a template
    coding_config = templates.apply_template("coding")
    assert "programming" in coding_config["system_prompt"].lower()
    print("   ✅ Applied coding template successfully")
    
    # Test creating custom template
    templates.create_custom_template(
        "test_template",
        "Test Template",
        "A template for testing",
        "You are a test assistant.",
        ["Hello, this is a test!"]
    )
    
    # Verify custom template
    custom_config = templates.apply_template("test_template")
    assert custom_config["system_prompt"] == "You are a test assistant."
    print("   ✅ Created and applied custom template")
    
    return True


def test_model_manager():
    """Test model management features"""
    print("\n🧪 Testing Model Manager...")
    
    config = ConfigManager()
    manager = ModelManager(config.cache_dir)
    
    # Test cache stats
    stats = manager.get_cache_stats()
    print(f"   ✅ Cache stats: {stats['total_models']} models, {stats['total_size_human']}")
    
    # Test cached models
    cached_models = manager.get_cached_models()
    print(f"   ✅ Found {len(cached_models)} cached models")
    
    # Test popular models
    popular = manager.get_popular_models()
    assert len(popular) > 0
    print(f"   ✅ Listed {len(popular)} popular models")
    
    # Test model availability check
    availability = manager.check_model_availability("meta-llama/Llama-3.2-1B")
    if availability["available"]:
        print("   ✅ Model availability check successful")
    else:
        print(f"   ⚠️  Model availability check: {availability.get('error', 'Unknown error')}")
    
    return True


def test_performance_monitor():
    """Test performance monitoring"""
    print("\n🧪 Testing Performance Monitor...")
    
    monitor = PerformanceMonitor()
    
    # Simulate some requests
    for i in range(3):
        monitor.start_request()
        time.sleep(0.1)  # Simulate processing
        monitor.end_request(success=True, tokens_generated=25)
    
    # Test stats
    stats = monitor.get_current_stats()
    assert stats["total_requests"] == 3
    print(f"   ✅ Recorded {stats['total_requests']} requests")
    print(f"   ✅ Average response time: {stats['avg_response_time_seconds']:.3f}s")
    
    # Test performance summary
    summary = monitor.get_performance_summary(hours=1)
    if "message" not in summary:
        print(f"   ✅ Performance summary: {summary['total_requests']} requests")
    
    # Test system info
    system_info = monitor.get_system_info()
    print(f"   ✅ System info: {system_info['cpu_count']} CPUs, {system_info['memory_total_gb']:.1f}GB RAM")
    
    monitor.stop()
    return True


def test_integration():
    """Test integration between components"""
    print("\n🧪 Testing Component Integration...")
    
    # Test config with all managers
    config = ConfigManager()
    conv_manager = ConversationManager()
    templates = ConversationTemplates()
    model_manager = ModelManager(config.cache_dir)
    perf_monitor = PerformanceMonitor()
    
    # Test workflow: apply template, simulate conversation, save it
    template_config = templates.apply_template("general")
    
    # Simulate a conversation
    test_conversation = [
        {"user": "Hello", "bot": "Hi! How can I help you today?"},
        {"user": "Tell me about AI", "bot": "AI stands for Artificial Intelligence..."}
    ]
    
    # Save conversation with metadata
    metadata = {
        "model_name": config.get_model_name(),
        "template_used": "general",
        "performance_data": perf_monitor.get_current_stats()
    }
    
    filename = conv_manager.save_conversation(
        test_conversation,
        "integration_test",
        metadata
    )
    
    print("   ✅ Integration test: template → conversation → save → metadata")
    
    perf_monitor.stop()
    return True


def main():
    """Run all advanced feature tests"""
    print("🚀 Advanced Features Test Suite")
    print("=" * 50)
    
    tests = [
        ("Conversation Management", test_conversation_management),
        ("Conversation Templates", test_conversation_templates),
        ("Model Manager", test_model_manager),
        ("Performance Monitor", test_performance_monitor),
        ("Integration", test_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All advanced features are working correctly!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
