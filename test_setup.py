#!/usr/bin/env python3
"""
Test script to verify the chatbot setup and GPU availability.
"""

import sys
import subprocess

def check_python():
    """Check Python version."""
    print(f"Python version: {sys.version}")
    return sys.version_info >= (3, 8)

def check_torch():
    """Check PyTorch installation and CUDA availability."""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU device: {torch.cuda.get_device_name(0)}")
            print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        return True
    except ImportError:
        print("PyTorch not installed")
        return False

def check_transformers():
    """Check transformers library."""
    try:
        import transformers
        print(f"Transformers version: {transformers.__version__}")
        return True
    except ImportError:
        print("Transformers not installed")
        return False

def check_other_deps():
    """Check other dependencies."""
    deps = ['yaml', 'colorama', 'accelerate']
    results = []
    
    for dep in deps:
        try:
            module = __import__(dep)
            if hasattr(module, '__version__'):
                print(f"{dep} version: {module.__version__}")
            else:
                print(f"{dep}: installed")
            results.append(True)
        except ImportError:
            print(f"{dep}: not installed")
            results.append(False)
    
    return all(results)

def main():
    """Main test function."""
    print("=" * 50)
    print("Chatbot Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Python version", check_python),
        ("PyTorch", check_torch),
        ("Transformers", check_transformers),
        ("Other dependencies", check_other_deps)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All tests passed! The chatbot should work correctly.")
    else:
        print("❌ Some tests failed. Please check the installation.")
    print("=" * 50)

if __name__ == "__main__":
    main()
