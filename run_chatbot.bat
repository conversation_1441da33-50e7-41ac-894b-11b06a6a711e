@echo off
REM Simple CLI Chatbot Setup and Run Script

echo ================================================
echo   Simple CLI Chatbot Setup
echo ================================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully!
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install requirements
    pause
    exit /b 1
)

echo.
echo ================================================
echo   Starting Chatbot...
echo ================================================
echo.

REM Run the chatbot
python chatbot.py

REM Deactivate virtual environment
deactivate

echo.
echo ================================================
echo   Chatbot session ended
echo ================================================
pause
