#!/usr/bin/env python3
"""
Test script for the chatbot
"""

from config_manager import Config<PERSON><PERSON><PERSON>
from model_loader import Model<PERSON>oader

def test_chatbot():
    """Test the chatbot functionality"""
    print("🧪 Testing Simple CLI Chatbot...")
    
    # Test configuration loading
    print("\n1. Testing configuration loading...")
    config = ConfigManager()
    print(f"   ✅ Model: {config.get_model_name()}")
    print(f"   ✅ Device: {config.device_config.type}")
    
    # Test model loading
    print("\n2. Testing model loading...")
    loader = ModelLoader(config)
    
    if loader.load_model():
        print("   ✅ Model loaded successfully!")
        
        # Test model info
        info = loader.get_model_info()
        print(f"   ✅ Model size: {info['model_size']:,} parameters")
        print(f"   ✅ Device: {info['device']}")
        if 'gpu_memory_allocated' in info:
            print(f"   ✅ GPU Memory: {info['gpu_memory_allocated']}")
        
        # Test text generation
        print("\n3. Testing text generation...")
        test_prompt = "Hello! How are you today?"
        print(f"   Input: {test_prompt}")
        
        response = loader.generate_response(test_prompt)
        print(f"   Output: {response}")
        
        if response and not response.startswith("Error"):
            print("   ✅ Text generation successful!")
        else:
            print("   ❌ Text generation failed!")
            return False
        
        # Test model unloading
        print("\n4. Testing model unloading...")
        loader.unload_model()
        print("   ✅ Model unloaded successfully!")
        
        print("\n🎉 All tests passed! The chatbot is ready to use.")
        return True
    else:
        print("   ❌ Model loading failed!")
        return False

if __name__ == "__main__":
    test_chatbot()
