# 🤖 Simple CLI Chatbot

A powerful, flexible command-line chatbot that supports different LLM models with GPU acceleration using CUDA 11.8.

## ✨ Features

- 🤖 **Flexible Model Support**: Easy to switch between different LLM models
- 🚀 **GPU Acceleration**: Optimized for CUDA 11.8 with automatic memory management
- ⚙️ **Configurable**: All parameters managed through YAML configuration
- 💬 **Interactive CLI**: Colorful command-line interface with conversation history
- 📁 **Conversation Management**: Save, load, and search conversation histories
- 📊 **Performance Monitoring**: Real-time performance metrics and statistics
- 🎭 **Conversation Templates**: Pre-built templates for different use cases
- 🔧 **Easy Setup**: Automated installation with Python virtual environment
- 🗂️ **Model Management**: Built-in model cache management and utilities

## 🚀 Quick Start

### Automated Setup (Recommended)

```bash
# Run the automated setup script
python setup.py
```

### Manual Setup

```bash
# Create virtual environment
python -m venv chatbot_env

# Activate virtual environment
# Windows:
chatbot_env\Scripts\activate
# Linux/Mac:
source chatbot_env/bin/activate

# Install dependencies
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install transformers accelerate pyyaml colorama tqdm huggingface-hub bitsandbytes psutil

# Run the chatbot
python cli_chatbot.py
```

### 1. Start the Chatbot

```bash
# On Windows
start_chatbot.bat

# On Linux/Mac
./start_chatbot.sh

# Or manually
source chatbot_env/bin/activate
python cli_chatbot.py
```

### 2. Chat Commands

#### Basic Commands
- `/help` - Show available commands
- `/info` - Display model information
- `/clear` - Clear conversation history
- `/history` - Show conversation history
- `/quit` - Exit the chatbot

#### Model Management
- `/model <model_name>` - Change the LLM model
- `/models` - Show model management options
- `/cache` - Show cache statistics

#### Conversation Management
- `/save [filename]` - Save current conversation
- `/load <filename>` - Load a saved conversation
- `/list` - List all saved conversations
- `/search <term>` - Search conversations for a term

#### Templates & Performance
- `/template <template_id>` - Apply a conversation template
- `/templates` - List available templates
- `/stats` - Show performance statistics

## Configuration

Edit `chatbot_config.yaml` to customize:

### Model Settings
```yaml
model:
  name: "meta-llama/Llama-3.2-1B"  # Change this to switch models
  temperature: 0.7
  max_new_tokens: 256
  # ... other parameters
```

### Device Settings
```yaml
device:
  type: "auto"  # auto, cuda, or cpu
  cuda_version: "11.8"
```

## Changing Models

### Method 1: Edit Configuration File
```yaml
model:
  name: "meta-llama/Llama-3.2-3B"  # or any other model
```

### Method 2: Use Chat Command
```
You: /model meta-llama/Llama-3.2-3B
```

### Popular Models to Try
- `meta-llama/Llama-3.2-1B` (Default - lightweight)
- `meta-llama/Llama-3.2-3B` (Better quality)
- `microsoft/DialoGPT-medium`
- `microsoft/DialoGPT-large`
- `facebook/blenderbot-400M-distill`

## System Requirements

- **GPU**: NVIDIA GPU with CUDA 11.8 support
- **RAM**: 8GB+ recommended (4GB minimum)
- **VRAM**: 4GB+ for 1B models, 8GB+ for 3B models
- **Python**: 3.8+

## Project Structure

```
simple_chatbot/
├── chatbot_env/              # Python virtual environment
├── models/                   # Downloaded model cache
├── chatbot_config.yaml       # Main configuration file
├── config_manager.py         # Configuration management
├── model_loader.py           # Model loading and GPU handling
├── cli_chatbot.py           # Main chatbot interface
├── start_chatbot.bat        # Windows startup script
├── chatbot_requirements.txt  # Python dependencies
└── README_CHATBOT.md        # This file
```

## Dependencies

- `torch` - PyTorch with CUDA 11.8 support
- `transformers` - Hugging Face transformers library
- `accelerate` - Model acceleration
- `pyyaml` - Configuration file parsing
- `colorama` - Colored terminal output
- `tqdm` - Progress bars
- `huggingface-hub` - Model downloading

## Troubleshooting

### CUDA Issues
- Ensure NVIDIA drivers are up to date
- Verify CUDA 11.8 is installed
- Check GPU memory with `/info` command

### Memory Issues
- Use smaller models (1B instead of 3B)
- The system automatically uses quantization for low-memory GPUs
- Close other GPU-intensive applications

### Model Loading Issues
- Check internet connection for first-time downloads
- Verify model name is correct
- Check available disk space in `./models/` directory

## Advanced Usage

### Custom System Prompts
Edit the `system_prompt` in `chatbot_config.yaml`:
```yaml
chat:
  system_prompt: "You are a helpful coding assistant specialized in Python."
```

### Memory Management
The chatbot automatically:
- Uses 4-bit quantization for GPUs with <16GB VRAM
- Uses 8-bit quantization for GPUs with <8GB VRAM
- Manages conversation history (default: last 10 messages)

### Logging
Logs are saved to `chatbot.log` with configurable levels:
```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "chatbot.log"
```

## License

This project is open source. See LICENSE file for details.
