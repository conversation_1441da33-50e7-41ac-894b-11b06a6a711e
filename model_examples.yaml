# Example configurations for different models

# GPT-2 Large (Default)
model_gpt2_large:
  name: "openai-community/gpt2-large"
  max_length: 150
  temperature: 0.7
  top_p: 0.9
  do_sample: true

# Llama 3.2 1B
model_llama_3_2_1b:
  name: "meta-llama/Llama-3.2-1B"
  max_length: 200
  temperature: 0.8
  top_p: 0.95
  do_sample: true

# DialoGPT Medium (Conversation focused)
model_dialogpt_medium:
  name: "microsoft/DialoGPT-medium"
  max_length: 100
  temperature: 0.7
  top_p: 0.9
  do_sample: true

# BlenderBot (Facebook's conversational AI)
model_blenderbot:
  name: "facebook/blenderbot-400M-distill"
  max_length: 128
  temperature: 0.6
  top_p: 0.9
  do_sample: true

# To use any of these configurations:
# 1. Copy the desired model configuration
# 2. Replace the 'model' section in config.yaml
# 3. Rename 'model_xxx' to 'model' in config.yaml
