"""
Model Loader for the Chatbot
Handles loading and managing LLM models with GPU support.
"""

import torch
import logging
import os
from typing import Optional, Tuple
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    GenerationConfig,
    BitsAndBytesConfig
)
from config_manager import ConfigManager


class ModelLoader:
    """Handles loading and managing LLM models"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the model loader
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
        self.model = None
        self.tokenizer = None
        self.device = None
        self.generation_config = None
        
        self._setup_device()
        self.config.create_models_directory()
    
    def _setup_device(self) -> None:
        """Setup the device for model loading"""
        device_type = self.config.device_config.type.lower()
        
        if device_type == "auto":
            if torch.cuda.is_available():
                self.device = "cuda"
                cuda_version = torch.version.cuda
                self.logger.info(f"CUDA available! Using GPU with CUDA {cuda_version}")
                self.logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
                self.logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            else:
                self.device = "cpu"
                self.logger.warning("CUDA not available, using CPU")
        elif device_type == "cuda":
            if torch.cuda.is_available():
                self.device = "cuda"
                self.logger.info("Using GPU (CUDA) as specified in config")
            else:
                self.logger.error("CUDA requested but not available, falling back to CPU")
                self.device = "cpu"
        else:
            self.device = "cpu"
            self.logger.info("Using CPU as specified in config")
    
    def load_model(self, model_name: Optional[str] = None) -> bool:
        """
        Load the specified model and tokenizer
        
        Args:
            model_name: Model name to load. If None, uses config default
            
        Returns:
            bool: True if successful, False otherwise
        """
        if model_name is None:
            model_name = self.config.get_model_name()
        
        try:
            self.logger.info(f"Loading model: {model_name}")

            # Load tokenizer
            self.logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=self.config.cache_dir,
                trust_remote_code=True
            )

            # Set pad token if not available
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.logger.info("Set pad_token to eos_token")
            
            # Configure model loading based on device and memory
            model_kwargs = {
                "cache_dir": self.config.cache_dir,
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
            }
            
            # Add device mapping for GPU
            if self.device == "cuda":
                model_kwargs["device_map"] = "auto"
                
                # Check available GPU memory and use quantization if needed
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                if gpu_memory < 8:  # Less than 8GB, use 8-bit quantization
                    self.logger.info("Using 8-bit quantization for low memory GPU")
                    model_kwargs["quantization_config"] = BitsAndBytesConfig(
                        load_in_8bit=True,
                        llm_int8_threshold=6.0
                    )
                elif gpu_memory < 16:  # Less than 16GB, use 4-bit quantization
                    self.logger.info("Using 4-bit quantization for medium memory GPU")
                    model_kwargs["quantization_config"] = BitsAndBytesConfig(
                        load_in_4bit=True,
                        bnb_4bit_compute_dtype=torch.float16,
                        bnb_4bit_use_double_quant=True,
                        bnb_4bit_quant_type="nf4"
                    )

            # Load model
            self.logger.info("Loading model...")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                **model_kwargs
            )
            
            # Move to device if not using device_map
            if self.device == "cpu" or "device_map" not in model_kwargs:
                self.model = self.model.to(self.device)
            
            # Setup generation configuration
            self._setup_generation_config()

            self.logger.info(f"Model loaded successfully on {self.device}")
            return True

        except Exception as e:
            self.logger.error(f"Error loading model {model_name}: {e}")
            return False
    
    def _setup_generation_config(self) -> None:
        """Setup generation configuration"""
        gen_params = self.config.get_generation_params()
        
        # Update pad_token_id if it was None in config
        if gen_params['pad_token_id'] is None:
            gen_params['pad_token_id'] = self.tokenizer.pad_token_id
        
        self.generation_config = GenerationConfig(**gen_params)
        self.logger.info("Generation configuration setup complete")
    
    def generate_response(self, prompt: str) -> str:
        """
        Generate a response for the given prompt
        
        Args:
            prompt: Input prompt text
            
        Returns:
            str: Generated response
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    generation_config=self.generation_config,
                    do_sample=self.generation_config.do_sample,
                    temperature=self.generation_config.temperature,
                    top_p=self.generation_config.top_p,
                    top_k=self.generation_config.top_k,
                    max_new_tokens=self.generation_config.max_new_tokens,
                    repetition_penalty=self.generation_config.repetition_penalty,
                    pad_token_id=self.generation_config.pad_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Remove the original prompt from the response
            if response.startswith(prompt):
                response = response[len(prompt):].strip()
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return f"Error generating response: {e}"
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        if self.model is None:
            return {"status": "No model loaded"}
        
        info = {
            "model_name": self.config.get_model_name(),
            "device": self.device,
            "model_size": sum(p.numel() for p in self.model.parameters()),
            "trainable_params": sum(p.numel() for p in self.model.parameters() if p.requires_grad),
        }
        
        if self.device == "cuda":
            info["gpu_memory_allocated"] = f"{torch.cuda.memory_allocated() / 1e9:.2f} GB"
            info["gpu_memory_cached"] = f"{torch.cuda.memory_reserved() / 1e9:.2f} GB"
        
        return info
    
    def unload_model(self) -> None:
        """Unload the current model to free memory"""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        if self.device == "cuda":
            torch.cuda.empty_cache()

        self.logger.info("Model unloaded and memory cleared")


if __name__ == "__main__":
    # Test the model loader
    config = ConfigManager()
    loader = ModelLoader(config)
    
    if loader.load_model():
        print("Model loaded successfully!")
        print(f"Model info: {loader.get_model_info()}")
    else:
        print("Failed to load model")
