#!/usr/bin/env python3
"""
Simple CLI Chatbot with LLM Support
A flexible chatbot that can work with different LLM models and supports GPU acceleration.
"""

import sys
import os
import logging
from typing import List, Dict
from colorama import init, Fore, Style
from config_manager import ConfigManager
from model_loader import ModelLoader


class ChatBot:
    """Main chatbot class that handles conversation flow"""
    
    def __init__(self, config_path: str = "chatbot_config.yaml"):
        """
        Initialize the chatbot
        
        Args:
            config_path: Path to configuration file
        """
        # Initialize colorama for colored output
        init(autoreset=True)
        
        self.config = ConfigManager(config_path)
        self.model_loader = ModelLoader(self.config)
        self.logger = logging.getLogger(__name__)
        
        self.conversation_history: List[Dict[str, str]] = []
        self.is_running = False
    
    def print_banner(self) -> None:
        """Print the chatbot banner"""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                     🤖 Simple CLI Chatbot                    ║
║                                                              ║
║  Model: {self.config.get_model_name():<48} ║
║  Device: {self.model_loader.device.upper():<47} ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
        print(banner)
    
    def print_help(self) -> None:
        """Print help information"""
        help_text = f"""
{Fore.YELLOW}Available Commands:{Style.RESET_ALL}
  {Fore.GREEN}/help{Style.RESET_ALL}     - Show this help message
  {Fore.GREEN}/info{Style.RESET_ALL}     - Show model information
  {Fore.GREEN}/clear{Style.RESET_ALL}    - Clear conversation history
  {Fore.GREEN}/history{Style.RESET_ALL}  - Show conversation history
  {Fore.GREEN}/model{Style.RESET_ALL}    - Change model (e.g., /model meta-llama/Llama-3.2-3B)
  {Fore.GREEN}/quit{Style.RESET_ALL}     - Exit the chatbot
  
{Fore.YELLOW}Tips:{Style.RESET_ALL}
  - Type your message and press Enter to chat
  - Use Ctrl+C to exit at any time
  - The bot remembers the last {self.config.chat_config.max_history} messages
"""
        print(help_text)
    
    def load_model(self) -> bool:
        """Load the model and return success status"""
        print(f"{Fore.YELLOW}🔄 Loading model...{Style.RESET_ALL}")
        success = self.model_loader.load_model()
        
        if success:
            print(f"{Fore.GREEN}✅ Model loaded successfully!{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}❌ Failed to load model. Please check your configuration.{Style.RESET_ALL}")
            return False
    
    def add_to_history(self, user_message: str, bot_response: str) -> None:
        """Add a conversation turn to history"""
        self.conversation_history.append({
            "user": user_message,
            "bot": bot_response
        })
        
        # Keep only the last N messages
        max_history = self.config.chat_config.max_history
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
    
    def build_prompt(self, user_message: str) -> str:
        """Build the prompt with system message and conversation history"""
        prompt_parts = [self.config.chat_config.system_prompt]
        
        # Add conversation history
        for turn in self.conversation_history:
            prompt_parts.append(f"Human: {turn['user']}")
            prompt_parts.append(f"Assistant: {turn['bot']}")
        
        # Add current user message
        prompt_parts.append(f"Human: {user_message}")
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    def handle_command(self, command: str) -> bool:
        """
        Handle special commands
        
        Args:
            command: Command string starting with /
            
        Returns:
            bool: True if should continue, False if should exit
        """
        command = command.lower().strip()
        
        if command == "/quit" or command == "/exit":
            return False
        
        elif command == "/help":
            self.print_help()
        
        elif command == "/info":
            info = self.model_loader.get_model_info()
            print(f"\n{Fore.CYAN}Model Information:{Style.RESET_ALL}")
            for key, value in info.items():
                print(f"  {key}: {value}")
            print()
        
        elif command == "/clear":
            self.conversation_history.clear()
            print(f"{Fore.GREEN}✅ Conversation history cleared.{Style.RESET_ALL}")
        
        elif command == "/history":
            if not self.conversation_history:
                print(f"{Fore.YELLOW}No conversation history.{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.CYAN}Conversation History:{Style.RESET_ALL}")
                for i, turn in enumerate(self.conversation_history, 1):
                    print(f"{Fore.BLUE}[{i}] You:{Style.RESET_ALL} {turn['user']}")
                    print(f"{Fore.GREEN}[{i}] Bot:{Style.RESET_ALL} {turn['bot']}\n")
        
        elif command.startswith("/model "):
            new_model = command[7:].strip()
            if new_model:
                print(f"{Fore.YELLOW}🔄 Changing model to: {new_model}{Style.RESET_ALL}")
                self.config.set_model_name(new_model)
                self.model_loader.unload_model()
                if self.load_model():
                    print(f"{Fore.GREEN}✅ Model changed successfully!{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}❌ Failed to load new model.{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ Please specify a model name. Example: /model meta-llama/Llama-3.2-3B{Style.RESET_ALL}")
        
        else:
            print(f"{Fore.RED}❌ Unknown command: {command}. Type /help for available commands.{Style.RESET_ALL}")
        
        return True
    
    def chat_loop(self) -> None:
        """Main chat loop"""
        self.is_running = True
        
        print(f"{Fore.GREEN}🚀 Chatbot is ready! Type /help for commands or start chatting.{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💡 Tip: Type /quit to exit{Style.RESET_ALL}\n")
        
        try:
            while self.is_running:
                # Get user input
                user_input = input(f"{Fore.BLUE}You: {Style.RESET_ALL}").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith("/"):
                    if not self.handle_command(user_input):
                        break
                    continue
                
                # Generate response
                print(f"{Fore.YELLOW}🤔 Thinking...{Style.RESET_ALL}", end="", flush=True)
                
                try:
                    prompt = self.build_prompt(user_input)
                    response = self.model_loader.generate_response(prompt)
                    
                    # Clear the "thinking" message
                    print("\r" + " " * 20 + "\r", end="")
                    
                    print(f"{Fore.GREEN}Bot: {Style.RESET_ALL}{response}\n")
                    
                    # Add to history
                    self.add_to_history(user_input, response)
                    
                except Exception as e:
                    print(f"\r{Fore.RED}❌ Error generating response: {e}{Style.RESET_ALL}\n")
                    self.logger.error(f"Error in chat loop: {e}")
        
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
        
        except Exception as e:
            print(f"\n{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")
            self.logger.error(f"Unexpected error in chat loop: {e}")
    
    def run(self) -> None:
        """Run the chatbot"""
        self.print_banner()
        
        if not self.load_model():
            print(f"{Fore.RED}❌ Cannot start chatbot without a loaded model.{Style.RESET_ALL}")
            sys.exit(1)
        
        self.chat_loop()


def main():
    """Main entry point"""
    try:
        # Check if config file exists
        config_file = "chatbot_config.yaml"
        if not os.path.exists(config_file):
            print(f"{Fore.RED}❌ Configuration file '{config_file}' not found.{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Please make sure the configuration file exists in the current directory.{Style.RESET_ALL}")
            sys.exit(1)
        
        # Start the chatbot
        chatbot = ChatBot(config_file)
        chatbot.run()
        
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to start chatbot: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
