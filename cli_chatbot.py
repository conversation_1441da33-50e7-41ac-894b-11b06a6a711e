#!/usr/bin/env python3
"""
Simple CLI Chatbot with LLM Support
A flexible chatbot that can work with different LLM models and supports GPU acceleration.
"""

import sys
import os
import logging
from typing import List, Dict
from colorama import init, Fore, Style
from config_manager import ConfigManager
from model_loader import Model<PERSON>oader
from conversation_manager import ConversationManager
from model_manager import Model<PERSON>anager
from conversation_templates import ConversationTemplates
from performance_monitor import PerformanceMonitor


class ChatBot:
    """Main chatbot class that handles conversation flow"""
    
    def __init__(self, config_path: str = "chatbot_config.yaml"):
        """
        Initialize the chatbot
        
        Args:
            config_path: Path to configuration file
        """
        # Initialize colorama for colored output
        init(autoreset=True)
        
        self.config = ConfigManager(config_path)
        self.model_loader = ModelLoader(self.config)
        self.conversation_manager = ConversationManager()
        self.model_manager = ModelManager(self.config.cache_dir)
        self.templates = ConversationTemplates()
        self.performance_monitor = PerformanceMonitor()
        self.logger = logging.getLogger(__name__)

        self.conversation_history: List[Dict[str, str]] = []
        self.is_running = False
    
    def print_banner(self) -> None:
        """Print the chatbot banner"""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                     🤖 Simple CLI Chatbot                    ║
║                                                              ║
║  Model: {self.config.get_model_name():<48} ║
║  Device: {self.model_loader.device.upper():<47} ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
        print(banner)
    
    def print_help(self) -> None:
        """Print help information"""
        help_text = f"""
{Fore.YELLOW}Available Commands:{Style.RESET_ALL}
  {Fore.GREEN}/help{Style.RESET_ALL}     - Show this help message
  {Fore.GREEN}/info{Style.RESET_ALL}     - Show model information
  {Fore.GREEN}/clear{Style.RESET_ALL}    - Clear conversation history
  {Fore.GREEN}/history{Style.RESET_ALL}  - Show conversation history
  {Fore.GREEN}/model{Style.RESET_ALL}    - Change model (e.g., /model meta-llama/Llama-3.2-3B)
  {Fore.GREEN}/save{Style.RESET_ALL}     - Save conversation (e.g., /save my_chat)
  {Fore.GREEN}/load{Style.RESET_ALL}     - Load conversation (e.g., /load my_chat)
  {Fore.GREEN}/list{Style.RESET_ALL}     - List saved conversations
  {Fore.GREEN}/search{Style.RESET_ALL}   - Search conversations (e.g., /search python)
  {Fore.GREEN}/models{Style.RESET_ALL}   - Show model management options
  {Fore.GREEN}/cache{Style.RESET_ALL}    - Show cache statistics
  {Fore.GREEN}/template{Style.RESET_ALL} - Use conversation template (e.g., /template coding)
  {Fore.GREEN}/templates{Style.RESET_ALL}- List available templates
  {Fore.GREEN}/stats{Style.RESET_ALL}    - Show performance statistics
  {Fore.GREEN}/quit{Style.RESET_ALL}     - Exit the chatbot
  
{Fore.YELLOW}Tips:{Style.RESET_ALL}
  - Type your message and press Enter to chat
  - Use Ctrl+C to exit at any time
  - The bot remembers the last {self.config.chat_config.max_history} messages
"""
        print(help_text)
    
    def load_model(self) -> bool:
        """Load the model and return success status"""
        print(f"{Fore.YELLOW}🔄 Loading model...{Style.RESET_ALL}")
        success = self.model_loader.load_model()
        
        if success:
            print(f"{Fore.GREEN}✅ Model loaded successfully!{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}❌ Failed to load model. Please check your configuration.{Style.RESET_ALL}")
            return False
    
    def add_to_history(self, user_message: str, bot_response: str) -> None:
        """Add a conversation turn to history"""
        self.conversation_history.append({
            "user": user_message,
            "bot": bot_response
        })
        
        # Keep only the last N messages
        max_history = self.config.chat_config.max_history
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
    
    def build_prompt(self, user_message: str) -> str:
        """Build the prompt with system message and conversation history"""
        prompt_parts = [self.config.chat_config.system_prompt]
        
        # Add conversation history
        for turn in self.conversation_history:
            prompt_parts.append(f"Human: {turn['user']}")
            prompt_parts.append(f"Assistant: {turn['bot']}")
        
        # Add current user message
        prompt_parts.append(f"Human: {user_message}")
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    def handle_command(self, command: str) -> bool:
        """
        Handle special commands
        
        Args:
            command: Command string starting with /
            
        Returns:
            bool: True if should continue, False if should exit
        """
        command = command.lower().strip()
        
        if command == "/quit" or command == "/exit":
            return False
        
        elif command == "/help":
            self.print_help()
        
        elif command == "/info":
            info = self.model_loader.get_model_info()
            print(f"\n{Fore.CYAN}Model Information:{Style.RESET_ALL}")
            for key, value in info.items():
                print(f"  {key}: {value}")
            print()
        
        elif command == "/clear":
            self.conversation_history.clear()
            print(f"{Fore.GREEN}✅ Conversation history cleared.{Style.RESET_ALL}")
        
        elif command == "/history":
            if not self.conversation_history:
                print(f"{Fore.YELLOW}No conversation history.{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.CYAN}Conversation History:{Style.RESET_ALL}")
                for i, turn in enumerate(self.conversation_history, 1):
                    print(f"{Fore.BLUE}[{i}] You:{Style.RESET_ALL} {turn['user']}")
                    print(f"{Fore.GREEN}[{i}] Bot:{Style.RESET_ALL} {turn['bot']}\n")
        
        elif command.startswith("/model "):
            new_model = command[7:].strip()
            if new_model:
                print(f"{Fore.YELLOW}🔄 Changing model to: {new_model}{Style.RESET_ALL}")
                self.config.set_model_name(new_model)
                self.model_loader.unload_model()
                if self.load_model():
                    print(f"{Fore.GREEN}✅ Model changed successfully!{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}❌ Failed to load new model.{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ Please specify a model name. Example: /model meta-llama/Llama-3.2-3B{Style.RESET_ALL}")

        elif command.startswith("/save"):
            parts = command.split(" ", 1)
            filename = parts[1].strip() if len(parts) > 1 else None
            self._save_conversation(filename)

        elif command.startswith("/load"):
            parts = command.split(" ", 1)
            if len(parts) > 1:
                filename = parts[1].strip()
                self._load_conversation(filename)
            else:
                print(f"{Fore.RED}❌ Please specify a filename. Example: /load my_chat{Style.RESET_ALL}")

        elif command == "/list":
            self._list_conversations()

        elif command.startswith("/search"):
            parts = command.split(" ", 1)
            if len(parts) > 1:
                query = parts[1].strip()
                self._search_conversations(query)
            else:
                print(f"{Fore.RED}❌ Please specify a search term. Example: /search python{Style.RESET_ALL}")

        elif command == "/models":
            self._show_model_options()

        elif command == "/cache":
            self._show_cache_stats()

        elif command.startswith("/template"):
            parts = command.split(" ", 1)
            if len(parts) > 1:
                template_id = parts[1].strip()
                self._apply_template(template_id)
            else:
                print(f"{Fore.RED}❌ Please specify a template. Example: /template coding{Style.RESET_ALL}")
                print(f"   Use '/templates' to see available templates.")

        elif command == "/templates":
            self._list_templates()

        elif command == "/stats":
            self._show_performance_stats()

        else:
            print(f"{Fore.RED}❌ Unknown command: {command}. Type /help for available commands.{Style.RESET_ALL}")

        return True
    
    def chat_loop(self) -> None:
        """Main chat loop"""
        self.is_running = True
        
        print(f"{Fore.GREEN}🚀 Chatbot is ready! Type /help for commands or start chatting.{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💡 Tip: Type /quit to exit{Style.RESET_ALL}\n")
        
        try:
            while self.is_running:
                # Get user input
                user_input = input(f"{Fore.BLUE}You: {Style.RESET_ALL}").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith("/"):
                    if not self.handle_command(user_input):
                        break
                    continue
                
                # Generate response
                print(f"{Fore.YELLOW}🤔 Thinking...{Style.RESET_ALL}", end="", flush=True)

                # Start performance monitoring
                self.performance_monitor.start_request()

                try:
                    prompt = self.build_prompt(user_input)
                    response = self.model_loader.generate_response(prompt)

                    # Count tokens (rough estimate)
                    token_count = len(response.split())

                    # End performance monitoring
                    response_time = self.performance_monitor.end_request(
                        success=True,
                        tokens_generated=token_count
                    )

                    # Clear the "thinking" message
                    print("\r" + " " * 20 + "\r", end="")

                    print(f"{Fore.GREEN}Bot: {Style.RESET_ALL}{response}")
                    print(f"{Fore.CYAN}⏱️  Response time: {response_time:.2f}s{Style.RESET_ALL}\n")

                    # Add to history
                    self.add_to_history(user_input, response)

                except Exception as e:
                    # End performance monitoring with error
                    self.performance_monitor.end_request(
                        success=False,
                        error_type=type(e).__name__
                    )

                    print(f"\r{Fore.RED}❌ Error generating response: {e}{Style.RESET_ALL}\n")
                    self.logger.error(f"Error in chat loop: {e}")
        
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")

        except Exception as e:
            print(f"\n{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")
            self.logger.error(f"Unexpected error in chat loop: {e}")

        finally:
            self.cleanup()
    
    def run(self) -> None:
        """Run the chatbot"""
        self.print_banner()
        
        if not self.load_model():
            print(f"{Fore.RED}❌ Cannot start chatbot without a loaded model.{Style.RESET_ALL}")
            sys.exit(1)
        
        self.chat_loop()

    def _save_conversation(self, filename: str = None) -> None:
        """Save current conversation to file"""
        if not self.conversation_history:
            print(f"{Fore.YELLOW}⚠️  No conversation to save.{Style.RESET_ALL}")
            return

        try:
            metadata = {
                "model_name": self.config.get_model_name(),
                "device": self.model_loader.device
            }

            filepath = self.conversation_manager.save_conversation(
                self.conversation_history, filename, metadata
            )

            print(f"{Fore.GREEN}✅ Conversation saved to: {filepath}{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}❌ Error saving conversation: {e}{Style.RESET_ALL}")

    def _load_conversation(self, filename: str) -> None:
        """Load conversation from file"""
        try:
            data = self.conversation_manager.load_conversation(filename)
            self.conversation_history = data.get("conversation", [])

            metadata = data.get("metadata", {})
            print(f"{Fore.GREEN}✅ Loaded conversation: {filename}{Style.RESET_ALL}")
            print(f"   Created: {metadata.get('created_at', 'unknown')}")
            print(f"   Model: {metadata.get('model_name', 'unknown')}")
            print(f"   Turns: {len(self.conversation_history)}")

        except FileNotFoundError:
            print(f"{Fore.RED}❌ Conversation file not found: {filename}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}❌ Error loading conversation: {e}{Style.RESET_ALL}")

    def _list_conversations(self) -> None:
        """List all saved conversations"""
        try:
            conversations = self.conversation_manager.list_conversations()

            if not conversations:
                print(f"{Fore.YELLOW}📁 No saved conversations found.{Style.RESET_ALL}")
                return

            print(f"\n{Fore.CYAN}📁 Saved Conversations:{Style.RESET_ALL}")
            print(f"{'Filename':<25} {'Created':<20} {'Turns':<6} {'Model':<20} {'Size'}")
            print("-" * 80)

            for conv in conversations:
                filename = conv['filename'][:24]
                created = conv['created_at'][:19] if conv['created_at'] != 'unknown' else 'unknown'
                turns = str(conv['total_turns'])
                model = conv['model_used'][:19]
                size = conv['size']

                print(f"{filename:<25} {created:<20} {turns:<6} {model:<20} {size}")

            # Show stats
            stats = self.conversation_manager.get_conversation_stats()
            print(f"\n{Fore.CYAN}📊 Statistics:{Style.RESET_ALL}")
            print(f"   Total conversations: {stats['total_conversations']}")
            print(f"   Total size: {stats['total_size']}")
            print(f"   Average turns: {stats['average_turns']:.1f}")
            print()

        except Exception as e:
            print(f"{Fore.RED}❌ Error listing conversations: {e}{Style.RESET_ALL}")

    def _search_conversations(self, query: str) -> None:
        """Search conversations for a term"""
        try:
            results = self.conversation_manager.search_conversations(query)

            if not results:
                print(f"{Fore.YELLOW}🔍 No conversations found containing '{query}'.{Style.RESET_ALL}")
                return

            print(f"\n{Fore.CYAN}🔍 Search Results for '{query}':{Style.RESET_ALL}")

            for result in results:
                print(f"\n{Fore.GREEN}📄 {result['filename']}{Style.RESET_ALL}")
                print(f"   Created: {result['created_at'][:19] if result['created_at'] != 'unknown' else 'unknown'}")
                print(f"   Model: {result['model_used']}")
                print(f"   Matches: {result['total_matches']}")

                # Show first few matches
                for i, match in enumerate(result['matches'][:3]):
                    print(f"   Turn {match['turn_number']}:")
                    user_text = match['user'][:100] + "..." if len(match['user']) > 100 else match['user']
                    bot_text = match['bot'][:100] + "..." if len(match['bot']) > 100 else match['bot']
                    print(f"     You: {user_text}")
                    print(f"     Bot: {bot_text}")

                if len(result['matches']) > 3:
                    print(f"     ... and {len(result['matches']) - 3} more matches")

            print()

        except Exception as e:
            print(f"{Fore.RED}❌ Error searching conversations: {e}{Style.RESET_ALL}")

    def _show_model_options(self) -> None:
        """Show model management options"""
        print(f"\n{Fore.CYAN}🤖 Model Management{Style.RESET_ALL}")
        print(f"Current model: {Fore.GREEN}{self.config.get_model_name()}{Style.RESET_ALL}")

        # Show cached models
        cached_models = self.model_manager.get_cached_models()
        if cached_models:
            print(f"\n{Fore.CYAN}📦 Cached Models:{Style.RESET_ALL}")
            for model in cached_models:
                current = "✓" if model["name"] == self.config.get_model_name() else " "
                print(f"  {current} {model['name']} ({model['size_human']})")

        # Show popular models
        print(f"\n{Fore.CYAN}⭐ Popular Models:{Style.RESET_ALL}")
        popular = self.model_manager.get_popular_models()
        for model in popular[:5]:
            cached = "📦" if any(m["name"] == model["name"] for m in cached_models) else "  "
            print(f"  {cached} {model['name']}")
            print(f"     {model['description']} ({model['size']})")
            print(f"     Use case: {model['use_case']}")

        print(f"\n{Fore.YELLOW}💡 Tips:{Style.RESET_ALL}")
        print(f"  • Use '/model <name>' to switch models")
        print(f"  • Use '/cache' to see disk usage")
        print(f"  • Smaller models use less memory but may have lower quality")
        print()

    def _show_cache_stats(self) -> None:
        """Show cache statistics and management options"""
        try:
            stats = self.model_manager.get_cache_stats()

            print(f"\n{Fore.CYAN}💾 Cache Statistics{Style.RESET_ALL}")
            print(f"  Models cached: {stats['total_models']}")
            print(f"  Cache size: {stats['total_size_human']}")
            print(f"  Disk free: {stats['disk_free_human']}")
            print(f"  Cache usage: {stats['cache_percentage']:.1f}% of disk")

            # Show individual models
            cached_models = self.model_manager.get_cached_models()
            if cached_models:
                print(f"\n{Fore.CYAN}📋 Cached Models:{Style.RESET_ALL}")
                print(f"{'Model':<35} {'Size':<10} {'Files'}")
                print("-" * 55)

                for model in cached_models:
                    name = model['name'][:34]
                    size = model['size_human']
                    files = str(model['files'])
                    print(f"{name:<35} {size:<10} {files}")

            # Show cleanup options
            if len(cached_models) > 3:
                print(f"\n{Fore.YELLOW}🧹 Cleanup Options:{Style.RESET_ALL}")
                print(f"  You have {len(cached_models)} models cached.")
                print(f"  Consider removing old models to free space.")
                print(f"  Note: Models will be re-downloaded when needed.")

            print()

        except Exception as e:
            print(f"{Fore.RED}❌ Error getting cache stats: {e}{Style.RESET_ALL}")

    def _list_templates(self) -> None:
        """List available conversation templates"""
        try:
            templates = self.templates.list_templates()

            print(f"\n{Fore.CYAN}📝 Available Conversation Templates{Style.RESET_ALL}")
            print(f"{'ID':<12} {'Name':<25} {'Description'}")
            print("-" * 70)

            for template in templates:
                template_id = template['id'][:11]
                name = template['name'][:24]
                description = template['description'][:40]
                print(f"{template_id:<12} {name:<25} {description}")

            print(f"\n{Fore.YELLOW}💡 Usage:{Style.RESET_ALL}")
            print(f"  Use '/template <id>' to apply a template")
            print(f"  Example: /template coding")
            print()

        except Exception as e:
            print(f"{Fore.RED}❌ Error listing templates: {e}{Style.RESET_ALL}")

    def _apply_template(self, template_id: str) -> None:
        """Apply a conversation template"""
        try:
            # Get template configuration
            config = self.templates.apply_template(template_id)

            # Update system prompt in config
            self.config.chat_config.system_prompt = config["system_prompt"]

            # Clear current conversation
            self.conversation_history.clear()

            print(f"{Fore.GREEN}✅ Applied template: {config['template_name']}{Style.RESET_ALL}")
            print(f"   System prompt updated")
            print(f"   Conversation history cleared")

            # Show starter message
            print(f"\n{Fore.GREEN}Bot: {Style.RESET_ALL}{config['starter_message']}\n")

        except FileNotFoundError:
            print(f"{Fore.RED}❌ Template not found: {template_id}{Style.RESET_ALL}")
            print(f"   Use '/templates' to see available templates.")
        except Exception as e:
            print(f"{Fore.RED}❌ Error applying template: {e}{Style.RESET_ALL}")

    def _show_performance_stats(self) -> None:
        """Show performance statistics"""
        try:
            # Current stats
            current_stats = self.performance_monitor.get_current_stats()

            print(f"\n{Fore.CYAN}📊 Performance Statistics{Style.RESET_ALL}")
            print(f"Session duration: {current_stats['session_duration_seconds']:.0f} seconds")
            print(f"Total requests: {current_stats['total_requests']}")
            print(f"Total errors: {current_stats['total_errors']}")
            print(f"Error rate: {current_stats['error_rate_percent']:.1f}%")
            print(f"Requests per minute: {current_stats['requests_per_minute']:.1f}")
            print(f"Average response time: {current_stats['avg_response_time_seconds']:.2f}s")
            print(f"Total tokens generated: {current_stats['total_tokens_generated']}")

            # Memory stats
            print(f"\n{Fore.CYAN}💾 Memory Usage{Style.RESET_ALL}")
            print(f"Current memory: {current_stats['current_memory_mb']:.1f} MB")
            print(f"Memory percentage: {current_stats['current_memory_percent']:.1f}%")

            # GPU stats (if available)
            if current_stats['current_gpu_memory_mb'] > 0:
                print(f"\n{Fore.CYAN}🎮 GPU Usage{Style.RESET_ALL}")
                print(f"GPU memory: {current_stats['current_gpu_memory_mb']:.1f} MB")
                print(f"GPU utilization: {current_stats['current_gpu_utilization_percent']:.1f}%")

            # Recent performance summary
            summary = self.performance_monitor.get_performance_summary(hours=1)
            if "message" not in summary:
                print(f"\n{Fore.CYAN}📈 Last Hour Summary{Style.RESET_ALL}")
                print(f"Requests: {summary['total_requests']}")
                print(f"Avg response time: {summary['avg_response_time_seconds']:.2f}s")
                print(f"Min/Max response time: {summary['min_response_time_seconds']:.2f}s / {summary['max_response_time_seconds']:.2f}s")
                print(f"Tokens generated: {summary['total_tokens_generated']}")

            # System info
            system_info = self.performance_monitor.get_system_info()
            print(f"\n{Fore.CYAN}🖥️  System Information{Style.RESET_ALL}")
            print(f"CPU cores: {system_info['cpu_count']}")
            print(f"CPU usage: {system_info['cpu_percent']:.1f}%")
            print(f"Total memory: {system_info['memory_total_gb']:.1f} GB")
            print(f"Available memory: {system_info['memory_available_gb']:.1f} GB")
            if system_info['gpu_available']:
                print(f"GPU: {system_info['gpu_name']}")
                print(f"GPU memory: {system_info['gpu_memory_total_gb']:.1f} GB")
            else:
                print("GPU: Not available")

            print()

        except Exception as e:
            print(f"{Fore.RED}❌ Error getting performance stats: {e}{Style.RESET_ALL}")

    def cleanup(self) -> None:
        """Cleanup resources before exit"""
        try:
            self.performance_monitor.stop()
            print(f"{Fore.GREEN}📊 Performance data saved{Style.RESET_ALL}")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


def main():
    """Main entry point"""
    try:
        # Check if config file exists
        config_file = "chatbot_config.yaml"
        if not os.path.exists(config_file):
            print(f"{Fore.RED}❌ Configuration file '{config_file}' not found.{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Please make sure the configuration file exists in the current directory.{Style.RESET_ALL}")
            sys.exit(1)
        
        # Start the chatbot
        chatbot = ChatBot(config_file)
        chatbot.run()
        
    except Exception as e:
        print(f"{Fore.RED}❌ Failed to start chatbot: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
