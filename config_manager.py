"""
Configuration Manager for the Chatbot
Handles loading and managing model configurations from YAML files.
"""

import yaml
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """Model configuration data class"""
    name: str
    max_length: int
    temperature: float
    top_p: float
    top_k: int
    do_sample: bool
    pad_token_id: Optional[int]
    max_new_tokens: int
    repetition_penalty: float


@dataclass
class DeviceConfig:
    """Device configuration data class"""
    type: str
    cuda_version: str


@dataclass
class ChatConfig:
    """Chat configuration data class"""
    system_prompt: str
    max_history: int


@dataclass
class LoggingConfig:
    """Logging configuration data class"""
    level: str
    file: str


class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: str = "chatbot_config.yaml"):
        """
        Initialize the configuration manager
        
        Args:
            config_path: Path to the configuration YAML file
        """
        self.config_path = config_path
        self.config_data = None
        self.model_config = None
        self.device_config = None
        self.chat_config = None
        self.logging_config = None
        self.cache_dir = None
        
        self.load_config()
        self.setup_logging()
    
    def load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config_data = yaml.safe_load(file)
            
            # Parse configurations
            self._parse_model_config()
            self._parse_device_config()
            self._parse_chat_config()
            self._parse_logging_config()
            self._parse_cache_dir()
            
            print(f"✅ Configuration loaded successfully from {self.config_path}")
            
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            raise
    
    def _parse_model_config(self) -> None:
        """Parse model configuration"""
        model_data = self.config_data.get('model', {})
        self.model_config = ModelConfig(
            name=model_data.get('name', 'meta-llama/Llama-3.2-1B'),
            max_length=model_data.get('max_length', 512),
            temperature=model_data.get('temperature', 0.7),
            top_p=model_data.get('top_p', 0.9),
            top_k=model_data.get('top_k', 50),
            do_sample=model_data.get('do_sample', True),
            pad_token_id=model_data.get('pad_token_id'),
            max_new_tokens=model_data.get('max_new_tokens', 256),
            repetition_penalty=model_data.get('repetition_penalty', 1.1)
        )
    
    def _parse_device_config(self) -> None:
        """Parse device configuration"""
        device_data = self.config_data.get('device', {})
        self.device_config = DeviceConfig(
            type=device_data.get('type', 'auto'),
            cuda_version=device_data.get('cuda_version', '11.8')
        )
    
    def _parse_chat_config(self) -> None:
        """Parse chat configuration"""
        chat_data = self.config_data.get('chat', {})
        self.chat_config = ChatConfig(
            system_prompt=chat_data.get('system_prompt', 'You are a helpful AI assistant.'),
            max_history=chat_data.get('max_history', 10)
        )
    
    def _parse_logging_config(self) -> None:
        """Parse logging configuration"""
        logging_data = self.config_data.get('logging', {})
        self.logging_config = LoggingConfig(
            level=logging_data.get('level', 'INFO'),
            file=logging_data.get('file', 'chatbot.log')
        )
    
    def _parse_cache_dir(self) -> None:
        """Parse cache directory"""
        self.cache_dir = self.config_data.get('cache_dir', './models')
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration"""
        log_level = getattr(logging, self.logging_config.level.upper(), logging.INFO)

        # Create file handler with UTF-8 encoding
        file_handler = logging.FileHandler(self.logging_config.file, encoding='utf-8')

        # Create console handler with UTF-8 encoding
        console_handler = logging.StreamHandler()

        # Set formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Configure root logger
        logging.basicConfig(
            level=log_level,
            handlers=[file_handler, console_handler]
        )
    
    def get_model_name(self) -> str:
        """Get the current model name"""
        return self.model_config.name
    
    def set_model_name(self, model_name: str) -> None:
        """
        Set a new model name and save to config file
        
        Args:
            model_name: New model name to use
        """
        self.model_config.name = model_name
        self.config_data['model']['name'] = model_name
        
        # Save updated configuration
        with open(self.config_path, 'w', encoding='utf-8') as file:
            yaml.dump(self.config_data, file, default_flow_style=False, indent=2)
        
        print(f"✅ Model name updated to: {model_name}")
    
    def get_generation_params(self) -> Dict[str, Any]:
        """Get parameters for text generation"""
        return {
            'max_length': self.model_config.max_length,
            'max_new_tokens': self.model_config.max_new_tokens,
            'temperature': self.model_config.temperature,
            'top_p': self.model_config.top_p,
            'top_k': self.model_config.top_k,
            'do_sample': self.model_config.do_sample,
            'repetition_penalty': self.model_config.repetition_penalty,
            'pad_token_id': self.model_config.pad_token_id
        }
    
    def create_models_directory(self) -> None:
        """Create models cache directory if it doesn't exist"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            print(f"✅ Created models directory: {self.cache_dir}")


if __name__ == "__main__":
    # Test the configuration manager
    config = ConfigManager()
    print(f"Current model: {config.get_model_name()}")
    print(f"Generation params: {config.get_generation_params()}")
