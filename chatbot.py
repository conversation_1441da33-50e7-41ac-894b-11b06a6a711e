import torch
import yaml
import os
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from colorama import Fore, Style, init
import sys

# Initialize colorama for Windows
init()

class ChatBot:
    def __init__(self, config_path="config.yaml"):
        """Initialize the chatbot with configuration."""
        self.config = self.load_config(config_path)
        self.device = self.setup_device()
        self.tokenizer = None
        self.model = None
        self.chat_history = []
        
    def load_config(self, config_path):
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
            return config
        except FileNotFoundError:
            print(f"{Fore.RED}Error: Configuration file '{config_path}' not found.{Style.RESET_ALL}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"{Fore.RED}Error parsing configuration file: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def setup_device(self):
        """Setup the device for computation (GPU/CPU)."""
        if self.config['device']['use_gpu'] and torch.cuda.is_available():
            device = f"cuda:{self.config['device']['cuda_device']}"
            print(f"{Fore.GREEN}Using GPU: {torch.cuda.get_device_name(self.config['device']['cuda_device'])}{Style.RESET_ALL}")
            print(f"{Fore.GREEN}CUDA Version: {torch.version.cuda}{Style.RESET_ALL}")
        else:
            device = "cpu"
            if self.config['device']['use_gpu']:
                print(f"{Fore.YELLOW}GPU requested but not available. Using CPU instead.{Style.RESET_ALL}")
            else:
                print(f"{Fore.BLUE}Using CPU{Style.RESET_ALL}")
        
        return device
    
    def load_model(self):
        """Load the tokenizer and model."""
        model_name = self.config['model']['name']
        print(f"{Fore.CYAN}Loading model: {model_name}...{Style.RESET_ALL}")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            
            # Add padding token if it doesn't exist
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.device.startswith('cuda') else torch.float32,
                device_map="auto" if self.device.startswith('cuda') else None,
                low_cpu_mem_usage=True
            )
            
            if not self.device.startswith('cuda'):
                self.model = self.model.to(self.device)
            
            print(f"{Fore.GREEN}Model loaded successfully!{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}Error loading model: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def generate_response(self, user_input):
        """Generate a response to user input."""
        try:
            # First check for predefined responses
            predefined_response = self._get_predefined_response(user_input)
            if predefined_response:
                return predefined_response
            
            # Check if this is Llama model for better prompting
            is_llama = "llama" in self.config['model']['name'].lower()
            
            if is_llama:
                # Use Llama's instruction format with extended context
                if self.chat_history:
                    # Include more chat history for longer conversations
                    conversation = f"{self.config['chat']['system_prompt']}\n\n"
                    recent_history = self.chat_history[-min(10, self.config['chat']['max_history']):]
                    for entry in recent_history:
                        conversation += f"User: {entry['user']}\nAssistant: {entry['bot']}\n\n"
                    prompt = f"{conversation}User: {user_input}\nAssistant:"
                else:
                    prompt = f"{self.config['chat']['system_prompt']}\n\nUser: {user_input}\nAssistant:"
            else:
                # Use GPT-2 style prompt
                prompt = f"A friendly conversation:\nPerson: {user_input}\nBot:"
            
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
            
            # Create attention mask
            attention_mask = torch.ones_like(inputs)
            
            # Generate response with model-specific parameters
            max_tokens = self.config['model'].get('max_new_tokens', self.config['model']['max_length']) if is_llama else 15
            temp = self.config['model']['temperature'] if is_llama else 0.8
            
            # Prepare generation parameters
            generation_params = {
                'inputs': inputs,
                'attention_mask': attention_mask,
                'max_new_tokens': max_tokens,
                'temperature': temp,
                'top_p': self.config['model']['top_p'],
                'do_sample': self.config['model']['do_sample'],
                'pad_token_id': self.tokenizer.eos_token_id,
                'eos_token_id': self.tokenizer.eos_token_id,
                'num_return_sequences': 1
            }
            
            # Add optional parameters if they exist in config
            if 'top_k' in self.config['model'] and self.config['model']['top_k'] > 0:
                generation_params['top_k'] = self.config['model']['top_k']
            
            if 'repetition_penalty' in self.config['model']:
                generation_params['repetition_penalty'] = self.config['model']['repetition_penalty']
            
            # Remove length_penalty as it's not supported by all models
            # if 'length_penalty' in self.config['model']:
            #     generation_params['length_penalty'] = self.config['model']['length_penalty']
            
            if 'no_repeat_ngram_size' in self.config['model']:
                generation_params['no_repeat_ngram_size'] = self.config['model']['no_repeat_ngram_size']
            
            if 'min_length' in self.config['model']:
                generation_params['min_length'] = inputs.shape[1] + self.config['model']['min_length']
            
            if 'early_stopping' in self.config['model']:
                generation_params['early_stopping'] = self.config['model']['early_stopping']
            
            if 'num_beams' in self.config['model'] and self.config['model']['num_beams'] > 1:
                generation_params['num_beams'] = self.config['model']['num_beams']
                generation_params['do_sample'] = False  # Beam search doesn't work with sampling
            
            if 'diversity_penalty' in self.config['model'] and self.config['model']['diversity_penalty'] > 0:
                generation_params['diversity_penalty'] = self.config['model']['diversity_penalty']
            
            if 'typical_p' in self.config['model'] and self.config['model']['typical_p'] < 1.0:
                generation_params['typical_p'] = self.config['model']['typical_p']
            
            with torch.no_grad():
                outputs = self.model.generate(**generation_params)
            
            # Decode response
            full_response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract response based on model type
            if is_llama and "Assistant:" in full_response:
                response = full_response.split("Assistant:")[-1].strip()
            elif not is_llama and "Bot:" in full_response:
                response = full_response.split("Bot:")[-1].strip()
            else:
                # Fallback: remove the input prompt
                input_text = self.tokenizer.decode(inputs[0], skip_special_tokens=True)
                if full_response.startswith(input_text):
                    response = full_response[len(input_text):].strip()
                else:
                    response = full_response.strip()
            
            # Clean the response thoroughly
            response = self._clean_response_simple(response)
            
            # Validate response quality
            if not self._is_good_response(response):
                response = self._get_fallback_response(user_input)
            
            return response
            
        except Exception as e:
            return f"Sorry, I encountered an error. How can I help you?"
    
    def _get_predefined_response(self, user_input):
        """Get predefined responses for common inputs."""
        user_lower = user_input.lower().strip()
        
        # Greeting responses
        greetings = {
            'hello': "Hello! Nice to meet you!",
            'hi': "Hi there! How can I help you?",
            'hey': "Hey! What's up?",
            'good morning': "Good morning! Hope you're having a great day!",
            'good afternoon': "Good afternoon! How are you doing?",
            'good evening': "Good evening! How was your day?",
            'howdy': "Howdy! How are you doing?"
        }
        
        # Common questions
        questions = {
            'how are you': "I'm doing well, thank you for asking! How are you?",
            'how are you doing': "I'm doing great! Thanks for asking. How about you?",
            "how's it going": "It's going well! How about with you?",
            'what is your name': "I'm a friendly AI assistant. What's your name?",
            'who are you': "I'm an AI chatbot here to help and chat with you!",
            'what are you': "I'm an artificial intelligence designed to have conversations.",
            'how old are you': "I don't have an age like humans do, but I'm here to chat!",
            'where are you from': "I exist in the digital world! Where are you from?",
            'python': "Yes, I know about Python! It's a popular programming language known for being easy to learn and very versatile.",
            'python programming': "Python is a great programming language! It's used for web development, data science, AI, and much more.",
            'do you know python': "Yes, I'm familiar with Python programming! What would you like to know about it?"
        }
        
        # Farewells
        farewells = {
            'bye': "Goodbye! Take care!",
            'goodbye': "Goodbye! Have a wonderful day!",
            'see you': "See you later! Take care!",
            'farewell': "Farewell! Until we meet again!",
            'later': "See you later! Have a good one!"
        }
        
        # Check all predefined responses
        all_responses = {**greetings, **questions, **farewells}
        
        if user_lower in all_responses:
            return all_responses[user_lower]
        
        # Check for partial matches
        for key, response in all_responses.items():
            if key in user_lower:
                return response
        
        return None
    
    def _is_good_response(self, response):
        """Check if the generated response is appropriate."""
        if not response or len(response.strip()) < 2:
            return False
        
        # Check for unwanted patterns
        bad_patterns = [
            'the_user', 'person:', 'bot:', 'user:', 'assistant:',
            'character:', 'narrator:', 'scene:', 'story:',
            '_', '__', '###', '***', '...',
            'university of sydney', 'developed by', 'customer service',
            'you have been developed', 'as part of a project',
            'assistants have been trained', 'natural language processing',
            'machine learning', 'nlp', 'these technologies'
        ]
        
        response_lower = response.lower()
        for pattern in bad_patterns:
            if pattern in response_lower:
                return False
        
        # Check if it's mostly punctuation or weird characters
        import re
        text_content = re.sub(r'[^\w\s]', '', response)
        if len(text_content.strip()) < 3:
            return False
        
        # Check for overly technical or meta content
        technical_words = [
            'sydney', 'university', 'client', 'assistants', 'trained',
            'technologies', 'underlying', 'appreciation', 'basically',
            'consists of', 'organized into', 'complex'
        ]
        
        if any(word in response_lower for word in technical_words):
            return False
        
        # Check if response is too long (likely rambling)
        if len(response) > 400:
            return False
        
        return True
    
    def _clean_response_simple(self, response):
        """Simple but effective response cleaning for concise responses."""
        if not response:
            return ""
        
        # Remove common unwanted patterns
        response = response.strip()
        
        # Stop at common narrative indicators and unwanted patterns
        stop_phrases = [
            "Animated character:", "A little kid", "The kid", "character:",
            "User:", "Human:", "Assistant:", "Person:", "Character:",
            "Scene:", "Setting:", "Story:", "Narrator:", "Bot:",
            "the_user", "_user", "__", "###", "***",
            "\n\nUser:", "\n\nBot:", "\n\nAssistant:", "\n\nHuman:",
            "You have been developed by", "University of", "as part of a project",
            "developed by", "created by", "designed by", "built by",
            "Assistants have been trained", "Natural Language Processing",
            "Machine Learning", "These technologies", "By understanding",
            "In addition to this", "For example, they", "1.", "2.", "3."
        ]
        
        for phrase in stop_phrases:
            if phrase in response:
                response = response.split(phrase)[0].strip()
        
        # Remove leading/trailing punctuation and underscores
        response = response.strip('_.,!?;: ')
        
        # Keep only the first 1-2 sentences for concise responses
        sentences = response.split('.')
        if sentences:
            good_sentences = []
            unwanted_keywords = [
                'assistants', 'trained', 'nlp', 'machine learning', 'technologies',
                'natural language processing', 'underlying', 'appreciation',
                'complex', 'basically', 'consists of', 'organized into'
            ]
            
            for i, sentence in enumerate(sentences[:2]):  # Only first 2 sentences
                sentence = sentence.strip()
                if len(sentence) > 5:
                    sentence_lower = sentence.lower()
                    # Skip sentences with technical jargon
                    if not any(keyword in sentence_lower for keyword in unwanted_keywords):
                        good_sentences.append(sentence)
                        if len(good_sentences) >= 2:  # Limit to 2 sentences max
                            break
            
            if good_sentences:
                response = '. '.join(good_sentences)
                if response and not response.endswith(('.', '!', '?')):
                    response += '.'
            else:
                # If no good sentences, return empty to trigger fallback
                response = ""
        
        # Remove any remaining unwanted characters
        import re
        response = re.sub(r'[_]{2,}', '', response)
        response = re.sub(r'[^\w\s.,!?\'"-]', '', response)
        response = re.sub(r'\s+', ' ', response)
        
        # Much shorter length limit for concise responses
        if len(response) > 300:
            response = response[:300].rsplit(' ', 1)[0] + "."
        
        return response.strip()
    
    def _get_fallback_response(self, user_input):
        """Provide appropriate fallback responses."""
        user_lower = user_input.lower().strip()
        
        if user_lower in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']:
            return "Hello! How are you doing today?"
        elif user_lower in ['how are you', 'how are you doing', "how's it going"]:
            return "I'm doing well, thank you for asking!"
        elif user_lower in ['what is your name', 'who are you', 'what are you']:
            return "I'm a helpful AI assistant."
        elif user_lower in ['bye', 'goodbye', 'see you', 'farewell']:
            return "Goodbye! Have a great day!"
        else:
            return "I'm here to help. What would you like to talk about?"
    
    def _clean_response_old(self, response):
        """Clean and filter the generated response."""
        if not response:
            return ""
        
        # Remove unwanted patterns at the beginning and end
        unwanted_patterns = [
            "\nUser:", "\nAssistant:", "\nHuman:", "\nAI:",
            "\n\nUser:", "\n\nAssistant:", "\n\nHuman:", "\n\nAI:",
            "User:", "Assistant:", "Human:", "AI:",
            "Assistant.Name:", "Assistant.Description:", ".Name:", ".Description:"
        ]
        
        # Clean the response
        cleaned_response = response.strip()
        
        # Remove unwanted patterns
        for pattern in unwanted_patterns:
            if pattern in cleaned_response:
                # Split on the pattern and take the first part
                parts = cleaned_response.split(pattern)
                cleaned_response = parts[0].strip()
                break
        
        # Remove any remaining control characters or weird formatting
        import re
        cleaned_response = re.sub(r'\s+', ' ', cleaned_response)  # Normalize whitespace
        cleaned_response = re.sub(r'^[^\w\s]*', '', cleaned_response)  # Remove leading special chars
        
        # Split into sentences and take only the first few coherent ones
        sentences = cleaned_response.split('.')
        if len(sentences) > 1:
            # Keep first 1-2 sentences if they make sense
            good_sentences = []
            for sentence in sentences[:3]:
                sentence = sentence.strip()
                if len(sentence) > 3 and not any(x in sentence.lower() for x in ['function', 'handler', 'method', 'code']):
                    good_sentences.append(sentence)
                elif not good_sentences:  # Keep first sentence even if it mentions code terms
                    good_sentences.append(sentence)
            
            if good_sentences:
                cleaned_response = '. '.join(good_sentences)
                if not cleaned_response.endswith('.'):
                    cleaned_response += '.'
        
        # Limit response length
        if len(cleaned_response) > 200:
            cleaned_response = cleaned_response[:200].rsplit(' ', 1)[0] + "..."
        
        return cleaned_response.strip()
    
    def add_to_history(self, user_input, bot_response):
        """Add the conversation to chat history."""
        self.chat_history.append({
            'user': user_input,
            'bot': bot_response
        })
        
        # Keep only the last max_history entries
        if len(self.chat_history) > self.config['chat']['max_history']:
            self.chat_history = self.chat_history[-self.config['chat']['max_history']:]
    
    def print_welcome(self):
        """Print welcome message."""
        print(f"\n{Fore.MAGENTA}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🤖 Simple CLI Chatbot{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}Model: {self.config['model']['name']}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}Device: {self.device}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Commands:{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /quit or /exit - Exit the chatbot{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /clear or /reset - Clear chat history{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /help - Show all commands{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /model - Show model info{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}{'='*60}{Style.RESET_ALL}\n")
    
    def print_help(self):
        """Print help message."""
        print(f"\n{Fore.YELLOW}Available commands:{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /quit or /exit - Exit the chatbot{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /clear - Clear chat history{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /reset - Reset conversation and clear history{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /history - Show conversation history{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /help - Show this help message{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}  /model - Show current model information{Style.RESET_ALL}\n")
    
    def print_history(self):
        """Print conversation history."""
        if not self.chat_history:
            print(f"{Fore.YELLOW}No conversation history yet.{Style.RESET_ALL}")
            return
        
        print(f"\n{Fore.CYAN}Conversation History:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*40}{Style.RESET_ALL}")
        for i, entry in enumerate(self.chat_history, 1):
            print(f"{Fore.GREEN}[{i}] You: {entry['user']}{Style.RESET_ALL}")
            print(f"{Fore.BLUE}[{i}] Bot: {entry['bot']}{Style.RESET_ALL}")
            print()
    
    def print_model_info(self):
        """Print current model information."""
        print(f"\n{Fore.CYAN}Current Model Information:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  Name: {self.config['model']['name']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  Max Length: {self.config['model']['max_length']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  Temperature: {self.config['model']['temperature']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  Device: {self.device}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  History Length: {len(self.chat_history)}/{self.config['chat']['max_history']}{Style.RESET_ALL}\n")
    
    def run(self):
        """Run the chatbot."""
        self.load_model()
        self.print_welcome()
        
        try:
            while True:
                # Get user input
                user_input = input(f"{Fore.GREEN}You: {Style.RESET_ALL}").strip()
                
                # Handle commands
                if user_input.lower() in ['/quit', '/exit']:
                    print(f"{Fore.MAGENTA}Goodbye! 👋{Style.RESET_ALL}")
                    break
                elif user_input.lower() == '/clear':
                    self.chat_history = []
                    print(f"{Fore.YELLOW}Chat history cleared.{Style.RESET_ALL}")
                    continue
                elif user_input.lower() == '/help':
                    self.print_help()
                    continue
                elif user_input.lower() == '/model':
                    self.print_model_info()
                    continue
                elif user_input.lower() == '/history':
                    self.print_history()
                    continue
                elif user_input.lower() == '/reset':
                    self.chat_history = []
                    print(f"{Fore.YELLOW}Chat session reset. Starting fresh conversation.{Style.RESET_ALL}")
                    continue
                elif not user_input:
                    continue
                
                # Generate and display response
                print(f"{Fore.BLUE}Bot: {Style.RESET_ALL}", end="")
                response = self.generate_response(user_input)
                print(response)
                
                # Add to history
                self.add_to_history(user_input, response)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.MAGENTA}Chatbot interrupted. Goodbye! 👋{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}An error occurred: {e}{Style.RESET_ALL}")

def main():
    """Main function to run the chatbot."""
    chatbot = ChatBot()
    chatbot.run()

if __name__ == "__main__":
    main()
