"""
Model Manager for the Chatbot
Utilities for managing LLM models, cache, and disk usage.
"""

import os
import shutil
import json
from pathlib import Path
from typing import List, Dict, Optional
from huggingface_hub import list_repo_files, repo_info
import requests


class ModelManager:
    """Manages LLM models and cache"""
    
    def __init__(self, cache_dir: str = "./models"):
        """
        Initialize the model manager
        
        Args:
            cache_dir: Directory where models are cached
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def get_cached_models(self) -> List[Dict[str, str]]:
        """
        Get list of locally cached models
        
        Returns:
            List[Dict]: Information about cached models
        """
        cached_models = []
        
        # Look for Hugging Face cache structure
        for model_dir in self.cache_dir.glob("models--*"):
            if model_dir.is_dir():
                # Extract model name from directory
                model_name = model_dir.name.replace("models--", "").replace("--", "/")
                
                # Calculate size
                total_size = sum(
                    f.stat().st_size for f in model_dir.rglob("*") if f.is_file()
                )
                
                # Get modification time
                mod_time = max(
                    f.stat().st_mtime for f in model_dir.rglob("*") if f.is_file()
                ) if any(model_dir.rglob("*")) else 0
                
                cached_models.append({
                    "name": model_name,
                    "path": str(model_dir),
                    "size_bytes": total_size,
                    "size_human": self._format_size(total_size),
                    "last_modified": mod_time,
                    "files": len(list(model_dir.rglob("*")))
                })
        
        return sorted(cached_models, key=lambda x: x["last_modified"], reverse=True)
    
    def get_cache_stats(self) -> Dict:
        """
        Get statistics about the model cache
        
        Returns:
            Dict: Cache statistics
        """
        cached_models = self.get_cached_models()
        
        total_size = sum(model["size_bytes"] for model in cached_models)
        
        # Get available disk space
        disk_usage = shutil.disk_usage(self.cache_dir)
        
        return {
            "total_models": len(cached_models),
            "total_size_bytes": total_size,
            "total_size_human": self._format_size(total_size),
            "disk_free_bytes": disk_usage.free,
            "disk_free_human": self._format_size(disk_usage.free),
            "disk_total_bytes": disk_usage.total,
            "disk_total_human": self._format_size(disk_usage.total),
            "cache_percentage": (total_size / disk_usage.total) * 100 if disk_usage.total > 0 else 0
        }
    
    def clear_model_cache(self, model_name: Optional[str] = None) -> bool:
        """
        Clear model cache (specific model or all)
        
        Args:
            model_name: Specific model to clear, or None for all
            
        Returns:
            bool: True if successful
        """
        try:
            if model_name:
                # Clear specific model
                model_dir_name = f"models--{model_name.replace('/', '--')}"
                model_path = self.cache_dir / model_dir_name
                
                if model_path.exists():
                    shutil.rmtree(model_path)
                    return True
                else:
                    return False
            else:
                # Clear all models
                for model_dir in self.cache_dir.glob("models--*"):
                    if model_dir.is_dir():
                        shutil.rmtree(model_dir)
                return True
                
        except Exception:
            return False
    
    def get_popular_models(self) -> List[Dict[str, str]]:
        """
        Get list of popular/recommended models
        
        Returns:
            List[Dict]: Popular models with descriptions
        """
        return [
            {
                "name": "meta-llama/Llama-3.2-1B",
                "description": "Lightweight Llama model, good for general chat",
                "size": "~1.2GB",
                "use_case": "General conversation, low memory",
                "quality": "Good"
            },
            {
                "name": "meta-llama/Llama-3.2-3B",
                "description": "Larger Llama model with better quality",
                "size": "~3.2GB", 
                "use_case": "Better conversations, medium memory",
                "quality": "Very Good"
            },
            {
                "name": "microsoft/DialoGPT-medium",
                "description": "Conversation-focused model",
                "size": "~350MB",
                "use_case": "Casual chat, very low memory",
                "quality": "Good"
            },
            {
                "name": "microsoft/DialoGPT-large",
                "description": "Larger conversation model",
                "size": "~775MB",
                "use_case": "Better chat quality, low memory",
                "quality": "Very Good"
            },
            {
                "name": "facebook/blenderbot-400M-distill",
                "description": "Fast conversational AI",
                "size": "~400MB",
                "use_case": "Quick responses, low memory",
                "quality": "Good"
            },
            {
                "name": "microsoft/CodeGPT-small-py",
                "description": "Python code generation",
                "size": "~500MB",
                "use_case": "Python coding assistance",
                "quality": "Good"
            }
        ]
    
    def check_model_availability(self, model_name: str) -> Dict:
        """
        Check if a model is available on Hugging Face
        
        Args:
            model_name: Name of the model to check
            
        Returns:
            Dict: Model availability information
        """
        try:
            # Try to get model info
            info = repo_info(model_name)
            
            # Get model files
            files = list_repo_files(model_name)
            model_files = [f for f in files if f.endswith(('.bin', '.safetensors', '.gguf'))]
            
            return {
                "available": True,
                "model_id": info.id,
                "author": info.author,
                "downloads": getattr(info, 'downloads', 0),
                "likes": getattr(info, 'likes', 0),
                "tags": getattr(info, 'tags', []),
                "model_files": model_files,
                "total_files": len(files)
            }
            
        except Exception as e:
            return {
                "available": False,
                "error": str(e)
            }
    
    def estimate_model_size(self, model_name: str) -> Dict:
        """
        Estimate model download size
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dict: Size estimation
        """
        try:
            files = list_repo_files(model_name)
            
            # Common model file extensions and their typical sizes
            size_estimates = {
                '.safetensors': 0,
                '.bin': 0,
                '.gguf': 0,
                'other': 0
            }
            
            for file in files:
                if file.endswith('.safetensors'):
                    # Estimate based on filename patterns
                    if '1B' in model_name or '1b' in model_name:
                        size_estimates['.safetensors'] += 1.2 * 1024**3  # 1.2GB
                    elif '3B' in model_name or '3b' in model_name:
                        size_estimates['.safetensors'] += 3.2 * 1024**3  # 3.2GB
                    elif '7B' in model_name or '7b' in model_name:
                        size_estimates['.safetensors'] += 7.5 * 1024**3  # 7.5GB
                    else:
                        size_estimates['.safetensors'] += 1.0 * 1024**3  # Default 1GB
                elif file.endswith('.bin'):
                    size_estimates['.bin'] += 0.5 * 1024**3  # 500MB default
                elif file.endswith('.gguf'):
                    size_estimates['.gguf'] += 1.0 * 1024**3  # 1GB default
                else:
                    size_estimates['other'] += 10 * 1024**2  # 10MB for other files
            
            total_size = sum(size_estimates.values())
            
            return {
                "estimated_size_bytes": int(total_size),
                "estimated_size_human": self._format_size(total_size),
                "breakdown": {k: self._format_size(v) for k, v in size_estimates.items() if v > 0}
            }
            
        except Exception as e:
            return {
                "estimated_size_bytes": 0,
                "estimated_size_human": "Unknown",
                "error": str(e)
            }
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def cleanup_old_models(self, keep_recent: int = 3) -> List[str]:
        """
        Clean up old models, keeping only the most recent ones
        
        Args:
            keep_recent: Number of recent models to keep
            
        Returns:
            List[str]: Names of removed models
        """
        cached_models = self.get_cached_models()
        
        if len(cached_models) <= keep_recent:
            return []
        
        # Sort by last modified (newest first)
        cached_models.sort(key=lambda x: x["last_modified"], reverse=True)
        
        # Remove old models
        removed_models = []
        for model in cached_models[keep_recent:]:
            if self.clear_model_cache(model["name"]):
                removed_models.append(model["name"])
        
        return removed_models


if __name__ == "__main__":
    # Test the model manager
    manager = ModelManager()
    
    print("Cached models:")
    for model in manager.get_cached_models():
        print(f"  {model['name']} - {model['size_human']}")
    
    print("\nCache stats:")
    stats = manager.get_cache_stats()
    print(f"  Total models: {stats['total_models']}")
    print(f"  Total size: {stats['total_size_human']}")
    print(f"  Free space: {stats['disk_free_human']}")
    
    print("\nPopular models:")
    for model in manager.get_popular_models()[:3]:
        print(f"  {model['name']} - {model['description']}")
