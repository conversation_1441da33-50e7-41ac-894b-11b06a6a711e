# Configuration for the chatbot
model:
  name: "meta-llama/Llama-3.2-1B"  # Change this to use different models
  max_length: 150  # Reduced for more concise responses
  temperature: 0.8  # Slightly higher for more natural conversation
  top_p: 0.9  # Nucleus sampling - consider top 90% probable tokens
  top_k: 50  # Consider only top 50 most likely tokens (0 = disabled)
  do_sample: true  # Enable sampling (vs greedy decoding)
  repetition_penalty: 1.2  # Increased to prevent repetitive content
  # length_penalty: 1.1  # Removed - not supported by this model
  no_repeat_ngram_size: 3  # Increased to prevent longer phrase repetition
  min_length: 10  # Reduced minimum length
  max_new_tokens: 150  # Reduced for shorter, focused responses
  early_stopping: true  # Enable to stop at natural endpoints
  num_beams: 1  # Beam search (1 = no beam search)
  diversity_penalty: 0.0  # Encourage diverse beam search results
  typical_p: 1.0  # Typical sampling parameter (1.0 = disabled)

# GPU settings
device:
  use_gpu: true
  cuda_device: 0  # Use GPU 0 if available

# Chat settings
chat:
  max_history: 8  # Reduced to prevent context overflow
  system_prompt: "You are a helpful AI assistant who gives concise, clear responses."
