"""
Conversation Manager for the Chatbot
Handles saving, loading, and managing conversation histories.
"""

import json
import os
import datetime
from typing import List, Dict, Optional
from pathlib import Path


class ConversationManager:
    """Manages conversation history import/export functionality"""
    
    def __init__(self, conversations_dir: str = "conversations"):
        """
        Initialize the conversation manager
        
        Args:
            conversations_dir: Directory to store conversation files
        """
        self.conversations_dir = Path(conversations_dir)
        self.conversations_dir.mkdir(exist_ok=True)
    
    def save_conversation(self, conversation_history: List[Dict[str, str]], 
                         filename: Optional[str] = None, 
                         metadata: Optional[Dict] = None) -> str:
        """
        Save a conversation to a JSON file
        
        Args:
            conversation_history: List of conversation turns
            filename: Optional custom filename
            metadata: Optional metadata to include
            
        Returns:
            str: Path to the saved file
        """
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"
        
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = self.conversations_dir / filename
        
        # Prepare conversation data
        base_metadata = {
            "created_at": datetime.datetime.now().isoformat(),
            "total_turns": len(conversation_history),
            "model_used": metadata.get("model_name", "unknown") if metadata else "unknown"
        }

        # Add additional metadata if provided
        if metadata:
            base_metadata.update(metadata)

        conversation_data = {
            "metadata": base_metadata,
            "conversation": conversation_history
        }
        
        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, indent=2, ensure_ascii=False)
        
        return str(filepath)
    
    def load_conversation(self, filename: str) -> Dict:
        """
        Load a conversation from a JSON file
        
        Args:
            filename: Name of the conversation file
            
        Returns:
            Dict: Conversation data with metadata and history
        """
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = self.conversations_dir / filename
        
        if not filepath.exists():
            raise FileNotFoundError(f"Conversation file not found: {filepath}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_conversations(self) -> List[Dict[str, str]]:
        """
        List all saved conversations
        
        Returns:
            List[Dict]: List of conversation summaries
        """
        conversations = []
        
        for filepath in self.conversations_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                metadata = data.get("metadata", {})
                conversations.append({
                    "filename": filepath.name,
                    "created_at": metadata.get("created_at", "unknown"),
                    "total_turns": metadata.get("total_turns", 0),
                    "model_used": metadata.get("model_used", "unknown"),
                    "size": f"{filepath.stat().st_size / 1024:.1f} KB"
                })
            except (json.JSONDecodeError, KeyError):
                # Skip invalid files
                continue
        
        # Sort by creation date (newest first)
        conversations.sort(key=lambda x: x["created_at"], reverse=True)
        return conversations
    
    def delete_conversation(self, filename: str) -> bool:
        """
        Delete a conversation file
        
        Args:
            filename: Name of the conversation file
            
        Returns:
            bool: True if deleted successfully
        """
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = self.conversations_dir / filename
        
        if filepath.exists():
            filepath.unlink()
            return True
        return False
    
    def export_conversation_to_text(self, filename: str, output_filename: Optional[str] = None) -> str:
        """
        Export a conversation to a readable text format
        
        Args:
            filename: Name of the conversation file
            output_filename: Optional output filename
            
        Returns:
            str: Path to the exported text file
        """
        conversation_data = self.load_conversation(filename)
        
        if output_filename is None:
            base_name = filename.replace('.json', '')
            output_filename = f"{base_name}.txt"
        
        if not output_filename.endswith('.txt'):
            output_filename += '.txt'
        
        output_path = self.conversations_dir / output_filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            # Write header
            metadata = conversation_data.get("metadata", {})
            f.write("=" * 60 + "\n")
            f.write("CONVERSATION EXPORT\n")
            f.write("=" * 60 + "\n")
            f.write(f"Created: {metadata.get('created_at', 'unknown')}\n")
            f.write(f"Model: {metadata.get('model_used', 'unknown')}\n")
            f.write(f"Total turns: {metadata.get('total_turns', 0)}\n")
            f.write("=" * 60 + "\n\n")
            
            # Write conversation
            for i, turn in enumerate(conversation_data.get("conversation", []), 1):
                f.write(f"[Turn {i}]\n")
                f.write(f"You: {turn.get('user', '')}\n")
                f.write(f"Bot: {turn.get('bot', '')}\n")
                f.write("-" * 40 + "\n\n")
        
        return str(output_path)
    
    def get_conversation_stats(self) -> Dict:
        """
        Get statistics about saved conversations
        
        Returns:
            Dict: Statistics about conversations
        """
        conversations = self.list_conversations()
        
        if not conversations:
            return {
                "total_conversations": 0,
                "total_size": "0 KB",
                "models_used": [],
                "date_range": "No conversations"
            }
        
        total_size = sum(
            (self.conversations_dir / conv["filename"]).stat().st_size 
            for conv in conversations
        )
        
        models_used = list(set(conv["model_used"] for conv in conversations))
        
        dates = [conv["created_at"] for conv in conversations if conv["created_at"] != "unknown"]
        date_range = "No dates available"
        if dates:
            dates.sort()
            if len(dates) == 1:
                date_range = dates[0][:10]  # Just the date part
            else:
                date_range = f"{dates[0][:10]} to {dates[-1][:10]}"
        
        return {
            "total_conversations": len(conversations),
            "total_size": f"{total_size / 1024:.1f} KB",
            "models_used": models_used,
            "date_range": date_range,
            "average_turns": sum(conv["total_turns"] for conv in conversations) / len(conversations)
        }
    
    def search_conversations(self, query: str) -> List[Dict]:
        """
        Search conversations for a specific term
        
        Args:
            query: Search term
            
        Returns:
            List[Dict]: Matching conversations with context
        """
        results = []
        query_lower = query.lower()
        
        for filepath in self.conversations_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                conversation = data.get("conversation", [])
                matches = []
                
                for i, turn in enumerate(conversation):
                    user_text = turn.get("user", "").lower()
                    bot_text = turn.get("bot", "").lower()
                    
                    if query_lower in user_text or query_lower in bot_text:
                        matches.append({
                            "turn_number": i + 1,
                            "user": turn.get("user", ""),
                            "bot": turn.get("bot", "")
                        })
                
                if matches:
                    metadata = data.get("metadata", {})
                    results.append({
                        "filename": filepath.name,
                        "created_at": metadata.get("created_at", "unknown"),
                        "model_used": metadata.get("model_used", "unknown"),
                        "matches": matches,
                        "total_matches": len(matches)
                    })
            
            except (json.JSONDecodeError, KeyError):
                continue
        
        return results


if __name__ == "__main__":
    # Test the conversation manager
    manager = ConversationManager()
    
    # Test data
    test_conversation = [
        {"user": "Hello!", "bot": "Hi there! How can I help you?"},
        {"user": "What's the weather like?", "bot": "I don't have access to current weather data, but I can help you with other questions!"}
    ]
    
    # Save test conversation
    filename = manager.save_conversation(
        test_conversation, 
        "test_conversation",
        {"model_name": "test-model", "test": True}
    )
    print(f"Saved conversation to: {filename}")
    
    # List conversations
    conversations = manager.list_conversations()
    print(f"Found {len(conversations)} conversations")
    
    # Get stats
    stats = manager.get_conversation_stats()
    print(f"Stats: {stats}")
